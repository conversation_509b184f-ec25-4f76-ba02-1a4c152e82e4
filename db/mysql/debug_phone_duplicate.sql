-- 调试手机号重复问题的SQL脚本
-- 请在数据库中执行这些查询来定位问题

-- 1. 查找手机号15257072059的所有记录（包括已删除的）
SELECT 
    id, name, phone, del_flag, 
    create_date, update_date,
    CASE 
        WHEN del_flag = '0' THEN '正常'
        WHEN del_flag = '1' THEN '已删除'
        ELSE '其他状态'
    END as status_desc
FROM qqc_competition_judge 
WHERE phone = '15257072059'
ORDER BY create_date;

-- 2. 查看表的约束信息
SELECT 
    CONSTRAINT_NAME, 
    CONSTRAINT_TYPE, 
    TABLE_NAME,
    COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_NAME = 'qqc_competition_judge' 
  AND TABLE_SCHEMA = DATABASE()
  AND COLUMN_NAME = 'phone';

-- 3. 查看表的索引信息
SHOW INDEX FROM qqc_competition_judge WHERE Column_name = 'phone';

-- 4. 查看所有重复的手机号（包括已删除记录）
SELECT 
    phone, 
    COUNT(*) as total_count,
    SUM(CASE WHEN del_flag = '0' THEN 1 ELSE 0 END) as active_count,
    SUM(CASE WHEN del_flag = '1' THEN 1 ELSE 0 END) as deleted_count,
    GROUP_CONCAT(
        CONCAT(name, '(', 
               CASE WHEN del_flag = '0' THEN '正常' ELSE '已删除' END, 
               ')') 
        ORDER BY create_date
    ) as records
FROM qqc_competition_judge 
WHERE phone IS NOT NULL AND phone != ''
GROUP BY phone 
HAVING COUNT(*) > 1
ORDER BY total_count DESC;

-- 5. 如果需要清理重复数据，请执行以下操作：
-- 注意：请先备份数据！

-- 方案A：物理删除已标记删除的重复记录
-- DELETE FROM qqc_competition_judge 
-- WHERE phone = '15257072059' AND del_flag = '1';

-- 方案B：清空重复手机号（如果记录确实重复）
-- UPDATE qqc_competition_judge 
-- SET phone = NULL 
-- WHERE phone = '15257072059' AND del_flag = '1';

-- 方案C：修改重复手机号（添加后缀）
-- UPDATE qqc_competition_judge 
-- SET phone = CONCAT(phone, '_deleted_', id) 
-- WHERE phone = '15257072059' AND del_flag = '1';