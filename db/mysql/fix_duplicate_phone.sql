-- 修复手机号重复问题的SQL脚本

-- 1. 查看手机号15257072059的重复记录
SELECT 
    id, name, phone, del_flag, create_date, update_date
FROM qqc_competition_judge 
WHERE phone = '15257072059'
ORDER BY create_date;

-- 2. 查看所有重复的手机号
SELECT 
    phone, 
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id) as ids,
    GROUP_CONCAT(name) as names
FROM qqc_competition_judge 
WHERE del_flag = '0' AND phone IS NOT NULL AND phone != ''
GROUP BY phone 
HAVING COUNT(*) > 1;

-- 3. 软删除重复记录（保留最新的一条）
-- 注意：执行前请先备份数据！
UPDATE qqc_competition_judge 
SET del_flag = '1', 
    update_date = NOW(),
    remarks = CONCAT(IFNULL(remarks, ''), ' [系统自动删除重复记录]')
WHERE phone = '15257072059' 
  AND del_flag = '0'
  AND id NOT IN (
    SELECT temp.id FROM (
      SELECT id 
      FROM qqc_competition_judge 
      WHERE phone = '15257072059' AND del_flag = '0'
      ORDER BY create_date DESC 
      LIMIT 1
    ) temp
  );

-- 4. 通用脚本：清理所有重复手机号（保留最新记录）
-- 警告：请在执行前仔细检查数据！
UPDATE qqc_competition_judge j1
SET j1.del_flag = '1', 
    j1.update_date = NOW(),
    j1.remarks = CONCAT(IFNULL(j1.remarks, ''), ' [系统自动删除重复手机号]')
WHERE j1.del_flag = '0' 
  AND j1.phone IS NOT NULL 
  AND j1.phone != ''
  AND EXISTS (
    SELECT 1 FROM (
      SELECT phone 
      FROM qqc_competition_judge 
      WHERE del_flag = '0' AND phone IS NOT NULL AND phone != ''
      GROUP BY phone 
      HAVING COUNT(*) > 1
    ) duplicates 
    WHERE duplicates.phone = j1.phone
  )
  AND j1.id NOT IN (
    SELECT temp.latest_id FROM (
      SELECT phone, MAX(create_date) as max_date, 
             SUBSTRING_INDEX(GROUP_CONCAT(id ORDER BY create_date DESC), ',', 1) as latest_id
      FROM qqc_competition_judge 
      WHERE del_flag = '0' AND phone IS NOT NULL AND phone != ''
      GROUP BY phone 
      HAVING COUNT(*) > 1
    ) temp 
    WHERE temp.phone = j1.phone
  );

-- 5. 验证清理结果
SELECT 
    '清理后重复手机号检查' as check_type,
    phone, 
    COUNT(*) as count
FROM qqc_competition_judge 
WHERE del_flag = '0' AND phone IS NOT NULL AND phone != ''
GROUP BY phone 
HAVING COUNT(*) > 1;