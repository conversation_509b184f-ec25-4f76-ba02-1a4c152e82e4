-- 实际数据检查SQL脚本

-- 1. 查看裁判表中手机号18868415999的所有记录（包括已删除）
SELECT 
    id, name, phone, del_flag, create_date, update_date,
    CASE 
        WHEN del_flag = '0' THEN '正常'
        WHEN del_flag = '1' THEN '已删除'
        ELSE '其他状态'
    END as status_desc
FROM qqc_competition_judge 
WHERE phone = '18868415999'
ORDER BY create_date DESC;

-- 2. 查看用户表中手机号18868415999的所有记录
SELECT 
    id, login_name, name, phone, del_flag, create_date, update_date,
    CASE 
        WHEN del_flag = '0' THEN '正常'
        WHEN del_flag = '1' THEN '已删除'
        ELSE '其他状态'
    END as status_desc
FROM sys_user 
WHERE phone = '18868415999' OR login_name = '18868415999'
ORDER BY create_date DESC;

-- 3. 查看约束信息
SHOW CREATE TABLE qqc_competition_judge;

-- 4. 查看索引信息  
SHOW INDEX FROM qqc_competition_judge WHERE Column_name = 'phone';

-- 5. 查看所有重复的手机号记录
SELECT 
    phone, 
    COUNT(*) as total_count,
    SUM(CASE WHEN del_flag = '0' THEN 1 ELSE 0 END) as active_count,
    SUM(CASE WHEN del_flag = '1' THEN 1 ELSE 0 END) as deleted_count,
    GROUP_CONCAT(
        CONCAT(id, '(', name, '-', 
               CASE WHEN del_flag = '0' THEN '正常' ELSE '已删除' END, 
               ')') 
        ORDER BY create_date
    ) as all_records
FROM qqc_competition_judge 
WHERE phone = '18868415999'
GROUP BY phone;

-- 6. 强制清理所有重复记录（如果需要手动清理）
-- 注意：执行前请备份数据！
-- DELETE FROM qqc_competition_judge WHERE phone = '18868415999';
-- DELETE FROM sys_user WHERE phone = '18868415999' OR login_name = '18868415999';