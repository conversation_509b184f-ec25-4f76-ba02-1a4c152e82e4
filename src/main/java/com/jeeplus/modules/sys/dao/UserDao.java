/**
 * Copyright &copy; 2015-2020 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.jeeplus.modules.sys.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.jeeplus.common.persistence.CrudDao;
import com.jeeplus.common.persistence.annotation.MyBatisDao;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.entity.UserOldPwd;

/**
 * 用户DAO接口
 * <AUTHOR>
 * @version 2014-05-16
 */
@MyBatisDao
public interface UserDao extends CrudDao<User> {

	/**
	 * 根据登录名称查询用户
	 * @param loginName
	 * @return
	 */
	public User getByLoginName(User user);

	/**
	 * 通过OfficeId获取用户列表，仅返回用户id和name（树查询用户时用）
	 * @param user
	 * @return
	 */
	public List<User> findUserByOfficeId(User user);

	/**
	 * 查询全部用户数目
	 * @return
	 */
	public long findAllCount(User user);

	/**
	 * 更新用户密码
	 * @param user
	 * @return
	 */
	public int updatePasswordById(User user);

	/**
	 * 更新登录信息，如：登录IP、登录时间
	 * @param user
	 * @return
	 */
	public int updateLoginInfo(User user);

	/**
	 * 删除用户角色关联数据
	 * @param user
	 * @return
	 */
	public int deleteUserRole(User user);

	/**
	 * 插入用户角色关联数据
	 * @param user
	 * @return
	 */
	public int insertUserRole(User user);

	/**
	 * 更新用户信息
	 * @param user
	 * @return
	 */
	public int updateUserInfo(User user);

	/**
	 * 插入好友
	 */
	public int insertFriend(@Param("id")String id, @Param("userId")String userId, @Param("friendId")String friendId);

	/**
	 * 查找好友
	 */
	public User findFriend(@Param("userId")String userId, @Param("friendId")String friendId);
	/**
	 * 删除好友
	 */
	public void deleteFriend(@Param("userId")String userId, @Param("friendId")String friendId);

	/**
	 *
	 * 获取我的好友列表
	 *
	 */
	public List<User> findFriends(User currentUser);

	/**
	 *
	 * 查询用户-->用来添加到常用联系人
	 *
	 */
	public List<User> searchUsers(User user);

	/**
	 *
	 */

	public List<User>  findListByOffice(User user);

	public User getByuId(@Param("uid") String uid);

	public User getByUnifiedLoginId(String unifiedLoginId);

	public void updateUid(User user);

	//根据登录名查询用户
	public User getByName (String username);

	public void insertUserOldPwd(UserOldPwd userOldPwd);
	
	public List<UserOldPwd> getOldPwd(@Param("userId")String userId);
	
	/**
	 * 根据登录名物理删除用户记录
	 * @param loginName 登录名
	 * @return 删除的用户数
	 */
	public int hardDeleteByLoginName(@Param("loginName") String loginName);
	
	/**
	 * 根据登录名删除用户角色关联
	 * @param loginName 登录名
	 * @return 删除的关联记录数
	 */
	public int deleteUserRoleByLoginName(@Param("loginName") String loginName);
}
