package com.jeeplus.modules.judge.judge.dao;


import com.jeeplus.common.persistence.CrudDao;
import com.jeeplus.common.persistence.annotation.MyBatisDao;
import com.jeeplus.modules.judge.judge.entity.CompetitionJudge;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 裁判DAO接口
 */
@MyBatisDao
public interface CompetitionJudgeDao extends CrudDao<CompetitionJudge> {

    /**
     * 根据用户ID查询裁判信息
     * @param userId 用户ID
     * @return 裁判信息
     */
    CompetitionJudge getByUserId(@Param("userId") String userId);

    /**
     * 根据手机号查询裁判信息
     * @param phone 手机号
     * @param delFlag 删除标记
     * @return 裁判信息
     */
    CompetitionJudge getByPhone(@Param("phone") String phone, @Param("delFlag") String delFlag);

    /**
     * 根据姓名模糊查询裁判列表
     * @param name 姓名
     * @return 裁判列表
     */
    List<CompetitionJudge> findByNameLike(@Param("name") String name);

    /**
     * 根据省市区查询裁判列表
     * @param province 省
     * @param city 市
     * @param area 区
     * @return 裁判列表
     */
    List<CompetitionJudge> findByArea(@Param("province") String province,
                                      @Param("city") String city,
                                      @Param("area") String area);

    /**
     * 获取所有可用的裁判列表（用于下拉框等）
     * @return 裁判列表
     */
    List<CompetitionJudge> findAllAvailable();

    /**
     * 物理删除指定手机号的所有记录
     * @param phone 手机号
     * @return 删除的记录数
     */
    int hardDeleteByPhone(@Param("phone") String phone);

}
