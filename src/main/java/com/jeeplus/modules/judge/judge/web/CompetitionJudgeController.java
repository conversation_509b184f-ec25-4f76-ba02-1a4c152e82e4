package com.jeeplus.modules.judge.judge.web;

import com.jeeplus.common.json.AjaxJson;
import com.jeeplus.common.persistence.Page;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.common.web.BaseController;
import com.jeeplus.modules.competition.registration.util.OssUploadUtil;
import com.jeeplus.modules.judge.judge.entity.CompetitionJudge;
import com.jeeplus.modules.judge.judge.service.CompetitionJudgeService;
import com.jeeplus.modules.sys.entity.Role;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.service.SystemService;
import com.jeeplus.modules.sys.utils.UserUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 裁判Controller
 */
@Controller
@RequestMapping("${adminPath}/qqc/judge")
public class CompetitionJudgeController extends BaseController {

    @Autowired
    private CompetitionJudgeService competitionJudgeService;
    @Autowired
    private SystemService systemService;
    private static final Map<String, String> AREA_CODE_MAP = new HashMap<>();
    static {
        AREA_CODE_MAP.put("330100", "杭州市");
        AREA_CODE_MAP.put("330200", "宁波市");
        AREA_CODE_MAP.put("330300", "温州市");
        AREA_CODE_MAP.put("330400", "嘉兴市");
        AREA_CODE_MAP.put("330500", "湖州市");
        AREA_CODE_MAP.put("330600", "绍兴市");
        AREA_CODE_MAP.put("330700", "金华市");
        AREA_CODE_MAP.put("330800", "衢州市");
        AREA_CODE_MAP.put("330900", "舟山市");
        AREA_CODE_MAP.put("331000", "台州市");
        AREA_CODE_MAP.put("331100", "丽水市");
    }


    @ModelAttribute
    public CompetitionJudge get(@RequestParam(required = false) String id) {
        CompetitionJudge entity = null;
        if (StringUtils.isNotBlank(id)) {
            entity = competitionJudgeService.get(id);
        }
        if (entity == null) {
            entity = new CompetitionJudge();
        }
        return entity;
    }

    /**
     * 裁判列表页面
     */
    @RequiresPermissions("qqc:judge:list")
    @RequestMapping(value = {"list", ""})
    public String list(CompetitionJudge competitionJudge, HttpServletResponse response,
                       HttpServletRequest request, Model model) {
        Page<CompetitionJudge> page = competitionJudgeService.findPage(
                new Page<CompetitionJudge>(request, response), competitionJudge);
        model.addAttribute("page", page);
        model.addAttribute("competitionJudge", competitionJudge);
        return "modules/judge/judge/CompetitionJudgeList";
    }

    /**
     * 查看，增加，编辑裁判表单页面
     */
    @RequiresPermissions(value = {"qqc:judge:view", "qqc:judge:add", "qqc:judge:edit"}, logical = Logical.OR)
    @RequestMapping(value = "form")
    public String form(CompetitionJudge competitionJudge, Model model) {

        User currentUser = UserUtils.getUser();
        boolean isTuanweiUser = false;
        if (currentUser != null && currentUser.getRoleList() != null) {
            for (Role role : currentUser.getRoleList()) {
                if ("municipal_committee".equals(role.getEnname())) {
                    isTuanweiUser = true;
                    break;
                }
            }
        }
        model.addAttribute("isTuanwei", isTuanweiUser);

        if (isTuanweiUser && StringUtils.isBlank(competitionJudge.getId())) {
            competitionJudge.setProvince("浙江省");

            String areaCode = currentUser.getAreaCode();
            if (StringUtils.isNotBlank(areaCode)) {
                String cityName = AREA_CODE_MAP.get(areaCode);
                if (cityName != null) {
                    competitionJudge.setCity(cityName);
                } else {
                    logger.warn("无法将地区代码 " + areaCode + " 映射到对应的城市中文名称。");
                }
            } else {
                logger.warn("团委用户 " + currentUser.getLoginName() + " 的地区代码(areaCode)为空。");
            }
        }

        model.addAttribute("userProvince","浙江省");
        model.addAttribute("userCity",AREA_CODE_MAP.get(currentUser.getAreaCode()));
        model.addAttribute("competitionJudge", competitionJudge);
        return "modules/judge/judge/CompetitionJudgeForm";
    }

    /**
     * 保存裁判
     */
    @RequiresPermissions(value = {"qqc:judge:add", "qqc:judge:edit"}, logical = Logical.OR)
    @RequestMapping(value = "save")
    @ResponseBody
    public Map<String, Object> save(CompetitionJudge competitionJudge, Model model) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (!beanValidator(model, competitionJudge)) {
                result.put("success", false);
                result.put("msg", "数据验证失败，请检查输入。");
                return result;
            }

            // 后端验证必填字段
            if (StringUtils.isBlank(competitionJudge.getName())) {
                result.put("success", false);
                result.put("msg", "姓名不能为空");
                return result;
            }

            if (StringUtils.isBlank(competitionJudge.getPhone())) {
                result.put("success", false);
                result.put("msg", "手机号不能为空");
                return result;
            }

            if (StringUtils.isBlank(competitionJudge.getProvince())) {
                result.put("success", false);
                result.put("msg", "省份不能为空");
                return result;
            }

            if (StringUtils.isBlank(competitionJudge.getCity())) {
                result.put("success", false);
                result.put("msg", "城市不能为空");
                return result;
            }

            if (StringUtils.isBlank(competitionJudge.getArea())) {
                result.put("success", false);
                result.put("msg", "区县不能为空");
                return result;
            }

            // 后端验证手机号格式
            if (StringUtils.isNotBlank(competitionJudge.getPhone())) {
                String phoneRegex = "^1[3-9]\\d{9}$";
                if (!competitionJudge.getPhone().matches(phoneRegex)) {
                    result.put("success", false);
                    result.put("msg", "请输入正确的11位手机号");
                    return result;
                }

                // 验证手机号是否已存在
                CompetitionJudgeService.PhoneCheckResult phoneCheck = 
                    competitionJudgeService.checkPhoneExist(competitionJudge.getPhone(), competitionJudge.getId());
                if (phoneCheck.isExist()) {
                    if (phoneCheck.isDeleted()) {
                        result.put("success", false);
                        result.put("code", "PHONE_DELETED_CONFLICT");
                        result.put("msg", "手机号 " + competitionJudge.getPhone() + " 已存在并被删除，是否要覆盖？");
                        result.put("deletedJudgeName", phoneCheck.getJudge().getName());
                        return result;
                    } else {
                        // 与正常记录冲突
                        result.put("success", false);
                        result.put("msg", "手机号 " + competitionJudge.getPhone() + " 已被使用，请换一个手机号！");
                        return result;
                    }
                }
            }

            // 处理HTML转义
            if (StringUtils.isNotBlank(competitionJudge.getName())) {
                competitionJudge.setName(StringEscapeUtils.unescapeHtml4(competitionJudge.getName()));
            }
            if (StringUtils.isNotBlank(competitionJudge.getIntroduction())) {
                competitionJudge.setIntroduction(StringEscapeUtils.unescapeHtml4(competitionJudge.getIntroduction()));
            }

            competitionJudgeService.save(competitionJudge);
            result.put("success", true);
            result.put("msg", "保存裁判信息成功");

        } catch (Exception e) {
            logger.error("保存裁判异常", e);
            result.put("success", false);

            String errorMessage = e.getMessage();
            if (errorMessage != null) {
                if (errorMessage.contains("Duplicate entry") && errorMessage.contains("phone")) {
                    result.put("msg", "手机号已存在，请换一个手机号！");
                } else if (errorMessage.contains("Duplicate entry")) {
                    result.put("msg", "数据重复，请检查输入信息！");
                } else {
                    result.put("msg", "保存裁判信息失败，请重试或联系管理员！");
                }
            } else {
                result.put("msg", "保存裁判信息失败，请重试或联系管理员！");
            }
        }
        return result;
    }
    
    /**
     * 保存裁判（强制覆盖已删除记录）
     */
    @RequiresPermissions(value = {"qqc:judge:add", "qqc:judge:edit"}, logical = Logical.OR)
    @RequestMapping(value = "saveWithOverride")
    @ResponseBody
    public Map<String, Object> saveWithOverride(CompetitionJudge competitionJudge, Model model) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (!beanValidator(model, competitionJudge)) {
                result.put("success", false);
                result.put("msg", "数据验证失败，请检查输入。");
                return result;
            }

            if (StringUtils.isBlank(competitionJudge.getName())) {
                result.put("success", false);
                result.put("msg", "姓名不能为空");
                return result;
            }

            if (StringUtils.isBlank(competitionJudge.getPhone())) {
                result.put("success", false);
                result.put("msg", "手机号不能为空");
                return result;
            }

            if (StringUtils.isBlank(competitionJudge.getProvince())) {
                result.put("success", false);
                result.put("msg", "省份不能为空");
                return result;
            }

            if (StringUtils.isBlank(competitionJudge.getCity())) {
                result.put("success", false);
                result.put("msg", "城市不能为空");
                return result;
            }

            if (StringUtils.isBlank(competitionJudge.getArea())) {
                result.put("success", false);
                result.put("msg", "区县不能为空");
                return result;
            }

            // 后端验证手机号格式
            if (StringUtils.isNotBlank(competitionJudge.getPhone())) {
                String phoneRegex = "^1[3-9]\\d{9}$";
                if (!competitionJudge.getPhone().matches(phoneRegex)) {
                    result.put("success", false);
                    result.put("msg", "请输入正确的11位手机号");
                    return result;
                }

                // 检查手机号状态
                CompetitionJudgeService.PhoneCheckResult phoneCheck = 
                    competitionJudgeService.checkPhoneExist(competitionJudge.getPhone(), competitionJudge.getId());
                if (phoneCheck.isExist()) {
                    if (!phoneCheck.isDeleted()) {
                        // 与正常记录冲突，不允许覆盖
                        result.put("success", false);
                        result.put("msg", "手机号 " + competitionJudge.getPhone() + " 已被使用，请换一个手机号！");
                        return result;
                    }
                    // 如果是已删除记录，saveWithOverride方法处理
                }
            }

            // 处理HTML转义
            if (StringUtils.isNotBlank(competitionJudge.getName())) {
                competitionJudge.setName(StringEscapeUtils.unescapeHtml4(competitionJudge.getName()));
            }
            if (StringUtils.isNotBlank(competitionJudge.getIntroduction())) {
                competitionJudge.setIntroduction(StringEscapeUtils.unescapeHtml4(competitionJudge.getIntroduction()));
            }

            competitionJudgeService.saveWithOverride(competitionJudge);
            result.put("success", true);
            result.put("msg", "覆盖保存裁判信息成功");

        } catch (Exception e) {
            logger.error("保存裁判异常", e);
            result.put("success", false);

            String errorMessage = e.getMessage();
            if (errorMessage != null) {
                if (errorMessage.contains("Duplicate entry") && errorMessage.contains("phone")) {
                    result.put("msg", "手机号已存在，请换一个手机号！");
                } else if (errorMessage.contains("Duplicate entry")) {
                    result.put("msg", "数据重复，请检查输入信息！");
                } else {
                    result.put("msg", "保存裁判信息失败，请重试或联系管理员！");
                }
            } else {
                result.put("msg", "保存裁判信息失败，请重试或联系管理员！");
            }
        }
        return result;
    }

    /**
     * 通用文件上传接口
     */
    @RequiresPermissions(value={"qqc:judge:add","qqc:judge:edit"}, logical=Logical.OR)
    @RequestMapping(value = "uploadFile", method = RequestMethod.POST)
    @ResponseBody
    public AjaxJson uploadFile(@RequestParam("file") MultipartFile file,
                               @RequestParam("fileType") String fileType) {
        AjaxJson j = new AjaxJson();
        try {
            OssUploadUtil.FileType type = OssUploadUtil.FileType.valueOf(fileType);
            String ossUrl = OssUploadUtil.uploadFile(file, type);

            j.setSuccess(true);
            j.setMsg("文件上传成功");
            j.getBody().put("ossUrl", ossUrl);

        } catch (IllegalArgumentException e) {
            j.setSuccess(false);
            j.setMsg("文件类型 '" + fileType + "' 无效，请检查。");
            logger.error("文件上传失败，无效的文件类型", e);
        } catch (Exception e) {
            j.setSuccess(false);
            j.setMsg("文件上传失败：" + e.getMessage());
            logger.error("文件上传失败", e);
        }
        return j;
    }

//    /**
//     * 上传照片
//     */
//    @RequiresPermissions(value = {"qqc:judge:add", "qqc:judge:edit"}, logical = Logical.OR)
//    @RequestMapping(value = "uploadPhoto", method = RequestMethod.POST)
//    @ResponseBody
//    public AjaxJson uploadPhoto(@RequestParam("file") MultipartFile file) {
//        AjaxJson j = new AjaxJson();
//        try {
//            // 上传到OSS
//            String ossUrl = OssUploadUtil.uploadFile(file, OssUploadUtil.FileType.IMAGE);
//
//            j.setSuccess(true);
//            j.setMsg("照片上传成功");
//            j.put("ossUrl", ossUrl);
//
//        } catch (Exception e) {
//            j.setSuccess(false);
//            j.setMsg("照片上传失败：" + e.getMessage());
//            logger.error("照片上传失败", e);
//        }
//        return j;
//    }

    /**
     * 删除裁判
     */
    @RequiresPermissions("qqc:judge:del")
    @RequestMapping(value = "delete")
    public String delete(CompetitionJudge competitionJudge, RedirectAttributes redirectAttributes) {
        competitionJudgeService.delete(competitionJudge);
        addMessage(redirectAttributes, "删除裁判成功");
        return "redirect:" + adminPath + "/qqc/judge/list?repage";
    }

    /**
     * 批量删除裁判
     */
    @RequiresPermissions("qqc:judge:del")
    @RequestMapping(value = "deleteAll")
    public String deleteAll(String ids, RedirectAttributes redirectAttributes) {
        String idArray[] = ids.split(",");
        for (String id : idArray) {
            competitionJudgeService.delete(competitionJudgeService.get(id));
        }
        addMessage(redirectAttributes, "删除裁判成功");
        return "redirect:" + adminPath + "/qqc/judge/list?repage";
    }

    /**
     * 获取裁判列表（Ajax）
     */
    @RequiresPermissions("qqc:judge:view")
    @RequestMapping(value = "listData")
    @ResponseBody
    public List<CompetitionJudge> listData(CompetitionJudge competitionJudge) {
        competitionJudge.setDelFlag(CompetitionJudge.DEL_FLAG_NORMAL);
        return competitionJudgeService.findList(competitionJudge);
    }

    /**
     * 根据姓名模糊查询裁判列表（Ajax）
     */
    @RequiresPermissions("qqc:judge:view")
    @RequestMapping(value = "searchByName")
    @ResponseBody
    public List<CompetitionJudge> searchByName(@RequestParam String name) {
        return competitionJudgeService.findByNameLike(name);
    }

    /**
     * 根据省市区查询裁判列表（Ajax）
     */
    @RequiresPermissions("qqc:judge:view")
    @RequestMapping(value = "searchByArea")
    @ResponseBody
    public List<CompetitionJudge> searchByArea(@RequestParam(required = false) String province,
                                               @RequestParam(required = false) String city,
                                               @RequestParam(required = false) String area) {
        return competitionJudgeService.findByArea(province, city, area);
    }

    /**
     * 获取所有可用的裁判列表（Ajax）
     */
    @RequiresPermissions("qqc:judge:view")
    @RequestMapping(value = "available")
    @ResponseBody
    public List<CompetitionJudge> available() {
        return competitionJudgeService.findAllAvailable();
    }

    /**
     * 验证手机号是否已存在（Ajax）
     */
    @RequiresPermissions("qqc:judge:view")
    @RequestMapping(value = "checkPhone")
    @ResponseBody
    public String checkPhone(@RequestParam String phone, @RequestParam(required = false) String id) {
        boolean exist = competitionJudgeService.isPhoneExist(phone, id);
        return exist ? "false" : "true";
    }

    /**
     * 重置裁判密码
     */
    @RequiresPermissions("qqc:judge:edit")
    @RequestMapping(value = "resetPassword")
    @ResponseBody
    public AjaxJson resetPassword(@RequestParam String judgeId, @RequestParam String newPassword) {
        AjaxJson j = new AjaxJson();
        try {

            CompetitionJudge judge = competitionJudgeService.get(judgeId);
            if (judge == null) {
                j.setSuccess(false);
                j.setMsg("裁判信息不存在");
                return j;
            }

            String userId = judge.getUserId();
            if (StringUtils.isBlank(userId)) {
                j.setSuccess(false);
                j.setMsg("裁判账号信息不存在");
                return j;
            }

            systemService.updatePasswordById(userId, judge.getPhone(), newPassword);
            
            j.setSuccess(true);
            j.setMsg("密码重置成功！新密码为：" + newPassword);
        } catch (Exception e) {
            e.printStackTrace();
            j.setSuccess(false);
            j.setMsg("密码重置失败：" + e.getMessage());
        }
        return j;
    }

}