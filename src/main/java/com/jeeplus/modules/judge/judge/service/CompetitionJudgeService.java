package com.jeeplus.modules.judge.judge.service;

import com.jeeplus.common.persistence.Page;
import com.jeeplus.common.service.CrudService;
import com.jeeplus.common.utils.StringUtils;
import com.jeeplus.modules.judge.judge.dao.CompetitionJudgeDao;
import com.jeeplus.modules.judge.judge.entity.CompetitionJudge;
import com.jeeplus.modules.sys.dao.RoleDao;
import com.jeeplus.modules.sys.entity.Role;
import com.jeeplus.modules.sys.entity.User;
import com.jeeplus.modules.sys.service.SystemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 裁判Service
 */
@Service
@Transactional(readOnly = true)
public class CompetitionJudgeService extends CrudService<CompetitionJudgeDao, CompetitionJudge> {
    @Autowired
    private SystemService systemService;
    @Autowired
    private RoleDao roleDao;
    public CompetitionJudge get(String id) {
        return super.get(id);
    }

    public List<CompetitionJudge> findList(CompetitionJudge competitionJudge) {
        return super.findList(competitionJudge);
    }

    public Page<CompetitionJudge> findPage(Page<CompetitionJudge> page, CompetitionJudge competitionJudge) {
        Page<CompetitionJudge> resultPage = super.findPage(page, competitionJudge);
        if (resultPage.getList() != null && !resultPage.getList().isEmpty()) {
            for (int i = 0; i < resultPage.getList().size(); i++) {
                CompetitionJudge judge = resultPage.getList().get(i);
            }
        }
        return resultPage;
    }

    @Transactional(readOnly = false)
    public void save(CompetitionJudge competitionJudge) {
        if (competitionJudge.getIsNewRecord()){
            User user = new User();
            user.setName(competitionJudge.getName());
            user.setLoginName(competitionJudge.getPhone());
            user.setPhone(competitionJudge.getPhone());
            user.setPassword(SystemService.entryptPassword(competitionJudge.getPhone()));
            user.setUserType("zj");
            List<Role> roleList = new ArrayList<>();
            roleList.add(roleDao.get("e211eb92b38b40f1bad69a8e53eb1ac4"));
            user.setRoleList(roleList);
            systemService.saveUser(user);
            competitionJudge.setUserId(user.getId());
        }
        super.save(competitionJudge);
    }

    @Transactional(readOnly = false)
    public void delete(CompetitionJudge competitionJudge) {
        super.delete(competitionJudge);
    }

    /**
     * 根据用户ID查询裁判信息
     */
    public CompetitionJudge getByUserId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return null;
        }
        return dao.getByUserId(userId);
    }

    /**
     * 根据手机号查询裁判信息
     */
    public CompetitionJudge getByPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return null;
        }
        return dao.getByPhone(phone, CompetitionJudge.DEL_FLAG_NORMAL);
    }

    /**
     * 根据姓名模糊查询裁判列表
     */
    public List<CompetitionJudge> findByNameLike(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        return dao.findByNameLike(name);
    }

    /**
     * 根据省市区查询裁判列表
     */
    public List<CompetitionJudge> findByArea(String province, String city, String area) {
        return dao.findByArea(province, city, area);
    }

    /**
     * 获取所有可用的裁判列表
     */
    public List<CompetitionJudge> findAllAvailable() {
        return dao.findAllAvailable();
    }

    /**
     * 验证手机号是否已存在
     */
    public boolean isPhoneExist(String phone, String id) {
        PhoneCheckResult result = checkPhoneExist(phone, id);
        return result.isExist();
    }
    
    /**
     * 检查手机号状态的详细结果
     */
    public PhoneCheckResult checkPhoneExist(String phone, String id) {
        if (StringUtils.isBlank(phone)) {
            return new PhoneCheckResult(false, false, null);
        }
        
        // 检查正常记录
        CompetitionJudge activeJudge = getByPhone(phone);
        if (activeJudge != null) {
            if (StringUtils.isNotBlank(id) && id.equals(activeJudge.getId())) {
                return new PhoneCheckResult(false, false, null);
            }
            return new PhoneCheckResult(true, false, activeJudge);
        }
        
        // 检查已删除记录
        CompetitionJudge deletedJudge = dao.getByPhone(phone, CompetitionJudge.DEL_FLAG_DELETE);
        if (deletedJudge != null) {
            // 编辑时，如果是当前记录，则不算重复
            if (StringUtils.isNotBlank(id) && id.equals(deletedJudge.getId())) {
                return new PhoneCheckResult(false, false, null);
            }
            return new PhoneCheckResult(true, true, deletedJudge);
        }
        
        return new PhoneCheckResult(false, false, null);
    }
    
    /**
     * 手机号检查结果类
     */
    public static class PhoneCheckResult {
        private boolean exist;           // 是否存在重复
        private boolean isDeleted;       // 重复记录是否为已删除状态
        private CompetitionJudge judge;  // 重复的记录
        
        public PhoneCheckResult(boolean exist, boolean isDeleted, CompetitionJudge judge) {
            this.exist = exist;
            this.isDeleted = isDeleted;
            this.judge = judge;
        }
        
        public boolean isExist() { return exist; }
        public boolean isDeleted() { return isDeleted; }
        public CompetitionJudge getJudge() { return judge; }
    }
    
    /**
     * 硬删除已软删除的手机号记录
     */
    @Transactional(readOnly = false)
    public void hardDeleteByPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return;
        }
        // 只删除已软删除的记录
        CompetitionJudge deletedJudge = dao.getByPhone(phone, CompetitionJudge.DEL_FLAG_DELETE);
        if (deletedJudge != null) {
            dao.delete(deletedJudge);
        }
    }
    
    /**
     * 保存裁判（跳过手机号重复验证，用于覆盖保存）
     */
    @Transactional(readOnly = false)
    public void saveWithoutPhoneCheck(CompetitionJudge competitionJudge) {
        if (competitionJudge.getIsNewRecord()){
            User user = new User();
            user.setName(competitionJudge.getName());
            user.setLoginName(competitionJudge.getPhone());
            user.setPhone(competitionJudge.getPhone());
            user.setPassword(SystemService.entryptPassword(competitionJudge.getPhone()));
            user.setUserType("zj");
            List<Role> roleList = new ArrayList<>();
            roleList.add(roleDao.get("e211eb92b38b40f1bad69a8e53eb1ac4"));
            user.setRoleList(roleList);
            systemService.saveUser(user);
            competitionJudge.setUserId(user.getId());
        }
        super.save(competitionJudge);
    }
    
    /**
     * 覆盖保存裁判（在同一事务中先删除已软删除记录，再保存新记录）
     */
    @Transactional(readOnly = false)
    public void saveWithOverride(CompetitionJudge competitionJudge) {
        String phone = competitionJudge.getPhone();
        logger.info("开始覆盖保存流程，手机号: {}", phone);
        
        // 在同一事务中先硬删除所有相关记录
        if (StringUtils.isNotBlank(phone)) {
            // 先删除用户表中对应的用户账号（根据登录名删除）
            systemService.hardDeleteUserByLoginName(phone);
            logger.info("删除用户表中登录名为 {} 的用户账号", phone);
            
            // 再物理删除该手机号的所有裁判记录（包括正常和已删除状态）
            int deletedCount = dao.hardDeleteByPhone(phone);
            logger.info("物理删除手机号 {} 的所有裁判记录，删除数量: {}", phone, deletedCount);
        }
        
        // 保存新记录
        if (competitionJudge.getIsNewRecord()){
            logger.info("创建新用户账号，手机号: {}", phone);
            User user = new User();
            user.setName(competitionJudge.getName());
            user.setLoginName(phone);
            user.setPhone(phone);
            user.setPassword(SystemService.entryptPassword(phone));
            user.setUserType("zj");
            List<Role> roleList = new ArrayList<>();
            roleList.add(roleDao.get("e211eb92b38b40f1bad69a8e53eb1ac4"));
            user.setRoleList(roleList);
            systemService.saveUser(user);
            competitionJudge.setUserId(user.getId());
            logger.info("用户账号创建成功，用户ID: {}", user.getId());
        }
        
        logger.info("准备插入新裁判记录，手机号: {}", phone);
        super.save(competitionJudge);
        logger.info("裁判记录插入成功，记录ID: {}", competitionJudge.getId());
    }

}