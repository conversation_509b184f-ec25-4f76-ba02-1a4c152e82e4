<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeeplus.modules.judge.judge.dao.CompetitionJudgeDao">

    <sql id="competitionJudgeColumns">
        a.id AS "id",
        a.user_id AS "userId",
        a.name AS "name",
        a.photo AS "photo",
        a.phone AS "phone",
        a.province AS "province",
        a.city AS "city",
        a.area AS "area",
        a.introduction AS "introduction",
        a.create_by AS "createBy.id",
        a.create_date AS "createDate",
        a.update_by AS "updateBy.id",
        a.update_date AS "updateDate",
        a.remarks AS "remarks",
        a.del_flag AS "delFlag",
        a.gender AS "gender"
    </sql>

    <sql id="competitionJudgeJoins">
    </sql>

    <select id="get" resultType="CompetitionJudge">
        SELECT
        <include refid="competitionJudgeColumns"/>
        FROM qqc_competition_judge a
        <include refid="competitionJudgeJoins"/>
        WHERE a.id = #{id}
    </select>

    <select id="findList" resultType="CompetitionJudge">
        SELECT
        <include refid="competitionJudgeColumns"/>
        FROM qqc_competition_judge a
        <include refid="competitionJudgeJoins"/>
        <where>
            a.del_flag = #{DEL_FLAG_NORMAL}
            <if test="ids != null and ids.length > 0">
                AND a.id IN
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="userId != null and userId != ''">
                AND a.user_id = #{userId}
            </if>
            <if test="name != null and name != ''">
                AND a.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND a.phone = #{phone}
            </if>
            <if test="province != null and province != ''">
                AND a.province = #{province}
            </if>
            <if test="city != null and city != ''">
                AND a.city = #{city}
            </if>
            <if test="area != null and area != ''">
                AND a.area = #{area}
            </if>
            <if test="gender != null and gender != ''">
                AND a.gender = #{gender}
            </if>
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.update_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="findAllList" resultType="CompetitionJudge">
        SELECT
        <include refid="competitionJudgeColumns"/>
        FROM qqc_competition_judge a
        <include refid="competitionJudgeJoins"/>
        <where>
            a.del_flag = #{DEL_FLAG_NORMAL}
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.update_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="getByUserId" resultType="CompetitionJudge">
        SELECT
        <include refid="competitionJudgeColumns"/>
        FROM qqc_competition_judge a
        <include refid="competitionJudgeJoins"/>
        WHERE a.user_id = #{userId} AND a.del_flag = '0'
    </select>

    <select id="getByPhone" resultType="CompetitionJudge">
        SELECT
        <include refid="competitionJudgeColumns"/>
        FROM qqc_competition_judge a
        <include refid="competitionJudgeJoins"/>
        WHERE a.phone = #{phone} AND a.del_flag = #{delFlag}
    </select>

    <select id="findByNameLike" resultType="CompetitionJudge">
        SELECT
        <include refid="competitionJudgeColumns"/>
        FROM qqc_competition_judge a
        <include refid="competitionJudgeJoins"/>
        WHERE a.name LIKE CONCAT('%', #{name}, '%') AND a.del_flag = #{DEL_FLAG_NORMAL}
        ORDER BY a.name
    </select>

    <select id="findByArea" resultType="CompetitionJudge">
        SELECT
        <include refid="competitionJudgeColumns"/>
        FROM qqc_competition_judge a
        <include refid="competitionJudgeJoins"/>
        <where>
            a.del_flag = #{DEL_FLAG_NORMAL}
            <if test="province != null and province != ''">
                AND a.province = #{province}
            </if>
            <if test="city != null and city != ''">
                AND a.city = #{city}
            </if>
            <if test="area != null and area != ''">
                AND a.area = #{area}
            </if>
        </where>
        ORDER BY a.province, a.city, a.area, a.name
    </select>

    <select id="findAllAvailable" resultType="CompetitionJudge">
        SELECT
        <include refid="competitionJudgeColumns"/>
        FROM qqc_competition_judge a
        <include refid="competitionJudgeJoins"/>
        WHERE a.del_flag = #{DEL_FLAG_NORMAL}
        ORDER BY a.name
    </select>

    <insert id="insert">
        INSERT INTO qqc_competition_judge(
            id,
            user_id,
            name,
            photo,
            phone,
            province,
            city,
            area,
            introduction,
            create_by,
            create_date,
            update_by,
            update_date,
            remarks,
            del_flag,
            gender
        ) VALUES (
                     #{id},
                     #{userId},
                     #{name},
                     #{photo},
                     #{phone},
                     #{province},
                     #{city},
                     #{area},
                     #{introduction},
                     #{createBy.id},
                     #{createDate},
                     #{updateBy.id},
                     #{updateDate},
                     #{remarks},
                     #{delFlag},
                     #{gender}
                 )
    </insert>

    <update id="update">
        UPDATE qqc_competition_judge SET
                                         user_id = #{userId},
                                         name = #{name},
                                         photo = #{photo},
                                         phone = #{phone},
                                         province = #{province},
                                         city = #{city},
                                         area = #{area},
                                         introduction = #{introduction},
                                         update_by = #{updateBy.id},
                                         update_date = #{updateDate},
                                         remarks = #{remarks},
                                         gender = #{gender}
        WHERE id = #{id}
    </update>

    <update id="delete">
        UPDATE qqc_competition_judge SET
            del_flag = #{DEL_FLAG_DELETE}
        WHERE id = #{id}
    </update>

    <delete id="hardDeleteByPhone">
        DELETE FROM qqc_competition_judge
        WHERE phone = #{phone}
    </delete>

</mapper>