<!DOCTYPE html>
<HTML>
<HEAD>
	<TITLE> ZTREE DEMO - addHoverDom / removeHoverDom / addDiyDom</TITLE>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="../../../css/demo.css" type="text/css">
	<link rel="stylesheet" href="../../../css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="../../../js/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.core-3.5.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.excheck-3.5.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.exedit-3.5.js"></script>
	<SCRIPT type="text/javascript">
		<!--

		var IDMark_Switch = "_switch",
		IDMark_Icon = "_ico",
		IDMark_Span = "_span",
		IDMark_Input = "_input",
		IDMark_Check = "_check",
		IDMark_Edit = "_edit",
		IDMark_Remove = "_remove",
		IDMark_Ul = "_ul",
		IDMark_A = "_a";
		
		var setting = {
			view: {
				addHoverDom: addHoverDom,
				removeHoverDom: removeHoverDom,
				addDiyDom: addDiyDom
			}
		};

		var zNodes =[
			{id:1, name:"hover事件显示控件", open:true,
				children:[
					   {id:11, name:"按钮1"},
					   {id:12, name:"按钮2"},
					   {id:13, name:"下拉框"},
					   {id:141, name:"文本1"},
					   {id:142, name:"文本2"},
					   {id:15, name:"超链接"}

				]},
			{id:2, name:"始终显示控件", open:true,
				children:[
					   {id:21, name:"按钮1"},
					   {id:22, name:"按钮2"},
					   {id:23, name:"下拉框"},
					   {id:24, name:"文本"},
					   {id:25, name:"超链接"}
				]}
	 	];

		function addHoverDom(treeId, treeNode) {
			if (treeNode.parentNode && treeNode.parentNode.id!=1) return;
			var aObj = $("#" + treeNode.tId + IDMark_A);
			if (treeNode.id == 11) {
				if ($("#diyBtn_"+treeNode.id).length>0) return;
				var editStr = "<span id='diyBtn_space_" +treeNode.id+ "' >&nbsp;</span><span class='button icon03' id='diyBtn_" +treeNode.id+ "' title='"+treeNode.name+"' onfocus='this.blur();'></span>";
				aObj.append(editStr);
				var btn = $("#diyBtn_"+treeNode.id);
				if (btn) btn.bind("click", function(){alert("diy Button for " + treeNode.name);});
			} else if (treeNode.id == 12) {
				if ($("#diyBtn_"+treeNode.id).length>0) return;
				var editStr = "<span class='button icon04' id='diyBtn_" +treeNode.id+ "' title='"+treeNode.name+"' onfocus='this.blur();'></span>";
				aObj.after(editStr);
				var btn = $("#diyBtn_"+treeNode.id);
				if (btn) btn.bind("click", function(){alert("diy Button for " + treeNode.name);});
			} else if (treeNode.id == 13) {
				if ($("#diyBtn_"+treeNode.id).length>0) return;
				var editStr = "<span id='diyBtn_space_" +treeNode.id+ "' >&nbsp;</span><select class='selDemo ' id='diyBtn_" +treeNode.id+ "'><option value=1>1</option><option value=2>2</option><option value=3>3</option></select>";
				aObj.after(editStr);
				var btn = $("#diyBtn_"+treeNode.id);
				if (btn) btn.bind("change", function(){alert("diy Select value="+btn.attr("value")+" for " + treeNode.name);});
			} else if (treeNode.id == 141) {
				if ($("#diyBtn_"+treeNode.id).length>0) return;
				var editStr = "<span class='test' id='diyBtn_" +treeNode.id+ "'>Text Demo...</span>";
				aObj.append(editStr);
			} else if (treeNode.id == 142) {
				if ($("#diyBtn_"+treeNode.id).length>0) return;
				var editStr = "<span id='diyBtn_" +treeNode.id+ "'>Text Demo...</span>";
				aObj.after(editStr);
			} else if (treeNode.id == 15) {
				if ($("#diyBtn1_"+treeNode.id).length>0) return;
				if ($("#diyBtn2_"+treeNode.id).length>0) return;
				var editStr = "<a id='diyBtn1_" +treeNode.id+ "' onclick='alert(1);return false;' style='margin:0 0 0 5px;'>链接1</a>" +
					"<a id='diyBtn2_" +treeNode.id+ "' onclick='alert(2);return false;' style='margin:0 0 0 5px;'>链接2</a>";
				aObj.append(editStr);
			}
		}

		function removeHoverDom(treeId, treeNode) {
			if (treeNode.parentTId && treeNode.getParentNode().id!=1) return;
			if (treeNode.id == 15) {
				$("#diyBtn1_"+treeNode.id).unbind().remove();
				$("#diyBtn2_"+treeNode.id).unbind().remove();
			} else {
				$("#diyBtn_"+treeNode.id).unbind().remove();
				$("#diyBtn_space_" +treeNode.id).unbind().remove();
			}
		}

		function addDiyDom(treeId, treeNode) {
			if (treeNode.parentNode && treeNode.parentNode.id!=2) return;
			var aObj = $("#" + treeNode.tId + IDMark_A);
			if (treeNode.id == 21) {
				var editStr = "<span class='demoIcon' id='diyBtn_" +treeNode.id+ "' title='"+treeNode.name+"' onfocus='this.blur();'><span class='button icon01'></span></span>";
				aObj.append(editStr);
				var btn = $("#diyBtn_"+treeNode.id);
				if (btn) btn.bind("click", function(){alert("diy Button for " + treeNode.name);});
			} else if (treeNode.id == 22) {
				var editStr = "<span class='demoIcon' id='diyBtn_" +treeNode.id+ "' title='"+treeNode.name+"' onfocus='this.blur();'><span class='button icon02'></span></span>";
				aObj.after(editStr);
				var btn = $("#diyBtn_"+treeNode.id);
				if (btn) btn.bind("click", function(){alert("diy Button for " + treeNode.name);});
			} else if (treeNode.id == 23) {
				var editStr = "<select class='selDemo' id='diyBtn_" +treeNode.id+ "'><option value=1>1</option><option value=2>2</option><option value=3>3</option></select>";
				aObj.after(editStr);
				var btn = $("#diyBtn_"+treeNode.id);
				if (btn) btn.bind("change", function(){alert("diy Select value="+btn.attr("value")+" for " + treeNode.name);});
			} else if (treeNode.id == 24) {
				var editStr = "<span id='diyBtn_" +treeNode.id+ "'>Text Demo...</span>";
				aObj.after(editStr);
			} else if (treeNode.id == 25) {
				var editStr = "<a id='diyBtn1_" +treeNode.id+ "' onclick='alert(1);return false;'>链接1</a>" +
					"<a id='diyBtn2_" +treeNode.id+ "' onclick='alert(2);return false;'>链接2</a>";
				aObj.after(editStr);
			}
		}

		$(document).ready(function(){
			$.fn.zTree.init($("#treeDemo"), setting, zNodes);
		});
		//-->
	</SCRIPT>
	<style type="text/css">
.ztree li span.demoIcon{padding:0 2px 0 10px;}
.ztree li span.button.icon01{margin:0; background: url(../../../css/zTreeStyle/img/diy/3.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon02{margin:0; background: url(../../../css/zTreeStyle/img/diy/4.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon03{margin:0; background: url(../../../css/zTreeStyle/img/diy/5.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon04{margin:0; background: url(../../../css/zTreeStyle/img/diy/6.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon05{margin:0; background: url(../../../css/zTreeStyle/img/diy/7.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
.ztree li span.button.icon06{margin:0; background: url(../../../css/zTreeStyle/img/diy/8.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle}
	</style>
 </HEAD>

<BODY>
<h1>添加自定义控件</h1>
<h6>[ 文件路径: super/diydom.html ]</h6>
<div class="content_wrap">
	<div class="zTreeDemoBackground left">
		<ul id="treeDemo" class="ztree"></ul>
	</div>
	<div class="right">
		<ul class="info">
			<li class="title"><h2>1、实现方法说明</h2>
				<ul class="list">
				<li>利用 setting.view.addHoverDom / removeHoverDom / addDiyDom 这几个参数的配置可以很容易的实现自定义控件的功能</li>
				<li class="highlight_red">添加自定义控件，请务必掌握 zTree 节点对象的命名规则，以保证正常添加 DOM 控件</li>
				<li class="highlight_red">如果添加标准的 select / checkbox / radio 等，请注意适当调整 zTree 的布局 css，保证 zTree 能正常显示</li>
				</ul>
			</li>
			<li class="title"><h2>2、setting 配置信息说明</h2>
				<ul class="list">
				<li>使用 setting.view.addHoverDom / removeHoverDom / addDiyDom 属性，详细请参见 API 文档中的相关内容</li>
				</ul>
			</li>
			<li class="title"><h2>3、treeNode 节点数据说明</h2>
				<ul class="list">
				<li>对 节点数据 没有特殊要求，用户可以根据自己的需求添加自定义属性</li>
				</ul>
			</li>
		</ul>
	</div>
</div>
</BODY>
</HTML>