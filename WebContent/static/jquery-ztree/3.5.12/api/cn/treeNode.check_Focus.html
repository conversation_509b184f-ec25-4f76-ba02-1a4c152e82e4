<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">treeNode.</span>check_Focus</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.excheck</span> 扩展 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>用于设置节点的 checkBox / radio 的 focus 状态。<span class="highlight_red">[setting.check.enable = true 时有效]</span></p>
			<p class="highlight_red">zTree 内部使用，请勿进行初始化 或 随意修改</p>
			<p>默认值：false</p>
		</div>
	</div>
	<h3>Boolean 格式说明</h3>
	<div class="desc">
	<p>true 表示当前鼠标移动到输入框内</p>
	<p>false 表示当前鼠标移动到输入框外</p>
	</div>
</div>
</div>