<div class="apiDetail">
<div>
	<h2><span>Number</span><span class="path">setting.edit.drag.</span>maxShowNodeNum</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.exedit</span> 扩展 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>拖拽多个兄弟节点时，浮动图层中显示的最大节点数。 多余的节点用...代替。<span class="highlight_red">[setting.edit.enable = true 时生效]</span></p>
			<p>默认值：5</p>
			<p class="highlight_red">请根据自己的需求适当调整此值</p>
		</div>
	</div>
	<h3>setting 举例</h3>
	<h4>1. 设置拖拽时最多可显示10个节点</h4>
	<pre xmlns=""><code>var setting = {
	edit: {
		enable: true,
		drag: {
			maxShowNodeNum: 10
		}
	}
};
......</code></pre>
</div>
</div>