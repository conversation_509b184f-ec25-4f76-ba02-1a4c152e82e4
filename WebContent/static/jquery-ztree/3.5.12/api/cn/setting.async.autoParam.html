<div class="apiDetail">
<div>
	<h2><span>Array(String)</span><span class="path">setting.async.</span>autoParam</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>异步加载时需要自动提交父节点属性的参数。<span class="highlight_red">[setting.async.enable = true 时生效]</span></p>
			<p>默认值：[ ]</p>
		</div>
	</div>
	<h3>Array(String) 格式说明</h3>
	<div class="desc">
	<p>1、将需要作为参数提交的属性名称，制作成 Array 即可，例如：["id", "name"]</p>
	<p>2、可以设置提交时的参数名称，例如 server 只接受 zId : ["id=zId"]</p>
	</div>
	<h3>setting 举例</h3>
	<h4>1. 设置 id 属性为自动提交的参数</h4>
	<pre xmlns=""><code>var setting = {
	async: {
		enable: true,
		url: "http://host/getNode.php",
		autoParam: ["id"]
	}
};
假设 异步加载 父节点(node = {id:1, name:"test"}) 的子节点时，将提交参数 id=1
......</code></pre>
	<h4>2. 设置 id 属性作为 zId 成为自动提交的参数</h4>
	<pre xmlns=""><code>var setting = {
	async: {
		enable: true,
		url: "http://host/getNode.php",
		autoParam: ["id=zId"]
	}
};
假设 对父节点 node = {id:1, name:"test"}，进行异步加载时，将提交参数 zId=1
......</code></pre>
</div>
</div>