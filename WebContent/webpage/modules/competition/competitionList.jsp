<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>赛事列表</title>
	<meta name="decorator" content="default"/>
	<style type="text/css">
	</style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
	<div class="ibox">
		<div class="ibox-title">
			<h5>赛事信息 </h5>
			<div class="ibox-tools">
				<a class="collapse-link">
					<i class="fa fa-chevron-up"></i>
				</a>
				<a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
					<i class="fa fa-wrench"></i>
				</a>
				<ul class="dropdown-menu dropdown-user">
					<li><a href="#">选项1</a>
					</li>
					<li><a href="#">选项2</a>
					</li>
				</ul>
				<a class="close-link">
					<i class="fa fa-times"></i>
				</a>
			</div>
		</div>

		<div class="ibox-content">
			<sys:message content="${message}"/>

			<!-- 查询条件 -->
			<div class="row">
				<div class="col-sm-12">
					<form:form id="searchForm" modelAttribute="competitionInfo" action="" method="post" class="form-inline">
						<input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
						<input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
						<div class="form-group">
							<span>项目名：&nbsp;</span>
							<form:input path="title" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200" class="form-control input-sm" placeholder="请直接填入项目名搜索"/>
							&nbsp;&nbsp;
							<span>类型：</span>
							<form:select path="type" class="form-control m-b">
								<form:option value="" label="全部"/>
								<form:option value="1" label="个人赛"/>
								<form:option value="2" label="团体赛"/>
							</form:select>
							&nbsp;&nbsp;
							<span>进度：</span>
							<form:select path="status" class="form-control m-b">
								<form:option value="" label="全部"/>
								<form:option value="0" label="未开始"/>
								<form:option value="1" label="报名中"/>
								<form:option value="2" label="报名结束"/>
								<form:option value="3" label="比赛中"/>
								<form:option value="4" label="已结束"/>
							</form:select>
						</div>
					</form:form>
					<br/>
				</div>
			</div>


			<!-- 工具栏 -->
			<div class="row">
				<div class="col-sm-12">
					<div class="pull-left">
						<shiro:hasPermission name="qqc:competition:add">
							<table:addRow width="1100px" height="900px" url="${ctx}/qqc/competition/form" title="赛事"></table:addRow><!-- 增加按钮 -->
						</shiro:hasPermission>
						<shiro:hasPermission name="qqc:competition:del">
							<table:delRow url="${ctx}/qqc/competition/deleteAll" id="competitionTable"></table:delRow><!-- 删除按钮 -->
						</shiro:hasPermission>
					</div>
					<div class="pull-right">
						<button class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
						<button class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
					</div>
				</div>
			</div>

			<table id="competitionTable" class="table table-striped table-bordered table-hover table-condensed dataTables-example dataTable no-footer">
				<thead>
				<tr>
					<th> <input type="checkbox" class="i-checks"></th>
					<th>赛事标题</th>
					<th>承办单位</th>
					<th>缩略图</th>
					<th>报名时间</th>
					<th>比赛时间</th>
					<th>类型</th>
					<th>状态</th>
					<th>比赛具体地点</th>
					<th>操作</th>
				</tr>
				</thead>
				<tbody>
				<c:forEach items="${page.list}" var="competition">
					<tr>
						<td> <input type="checkbox" id="${competition.id}" class="i-checks"></td>
						<td><a href="#" onclick="openDialogView('查看赛事', '${ctx}/qqc/competition/form?id=${competition.id}','1500px', '900px')">
								${fns:abbr(competition.title,50)}
						</a></td>
						<td>
								${competition.organizerUnit}
						</td>
						<td>
							<img src="${competition.thumbnailImg}" style="width:30px;height:30px;" />
						</td>
						<td>
							<fmt:formatDate value="${competition.registrationStartTime}" pattern="yyyy-MM-dd" /> 至
							<fmt:formatDate value="${competition.registrationEndTime}" pattern="yyyy-MM-dd" />
						</td>
						<td>
							<fmt:formatDate value="${competition.competitionStartTime}" pattern="yyyy-MM-dd" /> 至
							<fmt:formatDate value="${competition.competitionEndTime}" pattern="yyyy-MM-dd" />
						</td>
						<td>
								${competition.type == 1 ? '个人赛' : '团体赛'}
						</td>
						<td>
							<c:choose>
								<c:when test="${competition.status == 0}">未开始</c:when>
								<c:when test="${competition.status == 1}">报名中</c:when>
								<c:when test="${competition.status == 2}">报名结束</c:when>
								<c:when test="${competition.status == 3}">比赛中</c:when>
								<c:when test="${competition.status == 4}">已结束</c:when>
								<c:otherwise>未知</c:otherwise>
							</c:choose>
						</td>
						<td>
								${competition.competitionArea}
						</td>
						<td>
							<shiro:hasPermission name="qqc:competition:view">
								<a title="查看赛事" href="#" onclick="openDialogView('查看赛事', '${ctx}/qqc/competition/form?id=${competition.id}','700px', '500px')" class="btn btn-info btn-xs" >查看</a>
							</shiro:hasPermission>
							<shiro:hasRole name="cqc_admin">
								<shiro:hasPermission name="qqc:competition:edit">
									<a title="修改赛事" href="#" onclick="openDialog('修改赛事', '${ctx}/qqc/competition/form?id=${competition.id}','700px', '500px')" class="btn btn-success btn-xs" >修改</a>
								</shiro:hasPermission>
							</shiro:hasRole>
							<shiro:hasRole name="cqc_admin">
							<shiro:hasPermission name="qqc:competition:del">
								<a title="删除赛事" href="${ctx}/qqc/competition/delete?id=${competition.id}" onclick="return confirmx('确认要删除该赛事吗？', this.href)" class="btn btn-danger btn-xs">删除</a>
							</shiro:hasPermission>
							</shiro:hasRole>
							<shiro:hasPermission name="qqc:competition:view">
								<a title="报名详情" href="#" onclick="openDialogView('报名详情', '${ctx}/qqc/competition/registrationList?competitionId=${competition.id}','700px', '500px')" class="btn btn-primary btn-xs" >报名详情</a>
							</shiro:hasPermission>
							<shiro:hasPermission name="qqc:competition:edit">
								<a title="评审管理" href="#" onclick="openDialogView('评审管理', '${ctx}/judge/review/list?competitionId=${competition.id}&competitionTitle=${fns:urlEncode(competition.title)}','1200px', '800px')" class="btn btn-warning btn-xs" >评审管理</a>
							</shiro:hasPermission>
						</td>
					</tr>
				</c:forEach>
				</tbody>
			</table>
			<!-- 分页代码 -->
			<table:page page="${page}"></table:page>
			<br/>
			<br/>
		</div>
	</div>
</div>
</body>
</html> 