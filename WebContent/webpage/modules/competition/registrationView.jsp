<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="decorator" content="default"/>
    <title>报名信息详情</title>
    <style type="text/css">
        /* Custom styles for two-column layout */
        html, body, .wrapper {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        body.gray-bg {
            overflow: hidden; /* Prevent double scrollbars on the body */
        }
        .wrapper-content {
            height: 100%;
            padding: 0 !important;
        }
        .row.full-height {
            height: 100%;
            margin: 0;
        }
        .full-height-col {
            height: 100vh; /* Full viewport height */
            padding: 10px;
            display: flex;
            flex-direction: column;
        }
        .content-box {
            border: 1px solid #e7eaec;
            background-color: #fff;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* Important for child scrolling */
        }
        .content-box-body {
            flex-grow: 1;
            overflow-y: auto; /* Allow vertical scrolling */
            padding: 15px;
        }
        .pdf-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .no-pdf-message {
            text-align: center;
            padding-top: 50px;
            color: #888;
        }

        /* --- UI Optimization Styles --- */
        .detail-section {
            border: 1px solid #e7eaec;
            border-radius: 4px;
            margin-bottom: 20px;
            box-shadow: 0 1px 2px rgba(0,0,0,.05);
        }
        .detail-section-header {
            background-color: #f5f5f5;
            padding: 12px 15px;
            border-bottom: 1px solid #e7eaec;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }
        .detail-section-body {
            padding: 15px;
        }
        .detail-item {
            display: flex;
            padding: 8px 0;
            font-size: 14px;
            border-bottom: 1px solid #f0f0f0;
        }
        .detail-item:last-child {
            border-bottom: none;
        }
        .detail-label {
            flex: 0 0 120px;
            font-weight: 600;
            color: #676a6c;
            padding-right: 10px;
        }
        .detail-value {
            flex: 1;
            color: #333;
        }

        .member-card {
            border: 1px solid #e7eaec;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }
        .member-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            font-size: 14px;
        }
        .id-card-images img {
            max-width: 150px; /* smaller thumbnails */
            margin-top: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 3px;
        }
        .score-button-container {
            position: absolute;
            bottom: 20px;
            right: 20px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row full-height">

        <%-- Left Column for PDF Viewer --%>
        <div class="col-sm-7 full-height-col" style="padding-right: 5px;">
             <div class="content-box">
                <div class="content-box-body" style="padding:0;">
                    <c:if test="${not empty registration.projectPlanFile}">
                        <c:set var="pdfUrl" value="${registration.projectPlanFile}" />
                        <c:if test="${fn:startsWith(pdfUrl, 'http://')}">
                            <c:set var="pdfUrl" value="${fn:replace(pdfUrl, 'http://', 'https://')}" />
                        </c:if>
                        <iframe src="${pdfUrl}" class="pdf-iframe" title="项目计划书"></iframe>
                    </c:if>
                    <c:if test="${empty registration.projectPlanFile}">
                        <div class="no-pdf-message">
                            <h4>项目计划书未上传</h4>
                        </div>
                    </c:if>
                </div>
            </div>
        </div>

        <%-- Right Column for Project Details --%>
        <div class="col-sm-5 full-height-col" style="padding-left: 5px;">
            <div class="content-box">
                 <div class="content-box-body">
                    <h3 class="text-center" style="margin-top: 0; margin-bottom: 20px;">报名信息详情</h3>
                    
                    <div class="detail-section">
                        <h4 class="detail-section-header">基本信息</h4>
                        <div class="detail-section-body">
                            <div class="detail-item">
                                <span class="detail-label">参赛项目:</span>
                                <span class="detail-value">${registration.participantProject}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">报名途径:</span>
                                <span class="detail-value">${fns:getDictLabel(registration.registrationChannel, 'registration_channel', '未填写')}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">参赛地区:</span>
                                <span class="detail-value">${registration.competitionCityName} ${registration.competitionDistrictName}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">赛事分组:</span>
                                <span class="detail-value">
                                    ${fns:getDictLabel(registration.competitionGroup, 'competition_group', '未填写')}
                                    <c:if test="${not empty registration.competitionSubgroup}">
                                        - ${fns:getDictLabel(registration.competitionSubgroup, 'competition_subgroup', '')}
                                    </c:if>
                                </span>
                            </div>
                            <c:if test="${registration.competitionGroup == '3'}">
                                <div class="detail-item">
                                    <span class="detail-label">乡村振兴分组:</span>
                                    <span class="detail-value">${fns:getDictLabel(registration.ruralCompetitionSubgroup, 'rural_subgroup', '未填写')}</span>
                                </div>
                            </c:if>
                            <c:if test="${not empty registration.projectField}">
                                <div class="detail-item">
                                    <span class="detail-label">项目领域:</span>
                                    <span class="detail-value">${fns:getDictLabel(registration.projectField, 'project_field', '未填写')}</span>
                                </div>
                            </c:if>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4 class="detail-section-header">第一申报人信息</h4>
                        <div class="detail-section-body">
                            <div class="detail-item"><span class="detail-label">姓名:</span><span class="detail-value">${registration.firstApplicantName}</span></div>
                            <c:if test="${param.role != 'judge'}">
                                <div class="detail-item"><span class="detail-label">手机号:</span><span class="detail-value">${registration.firstApplicantMobile}</span></div>
                                <div class="detail-item"><span class="detail-label">邮箱:</span><span class="detail-value">${registration.firstApplicantEmail}</span></div>
                            </c:if>
                            <c:if test="${not empty registration.firstApplicantHukou}"><div class="detail-item"><span class="detail-label">户籍:</span><span class="detail-value">${registration.firstApplicantHukou}</span></div></c:if>
                            <c:if test="${not empty registration.firstApplicantPosition}"><div class="detail-item"><span class="detail-label">职位:</span><span class="detail-value">${registration.firstApplicantPosition}</span></div></c:if>
                            <div class="detail-item">
                                <span class="detail-label">性别:</span>
                                <span class="detail-value">
                                    <c:choose>
                                        <c:when test="${registration.firstApplicantGender == 1}">男</c:when>
                                        <c:when test="${registration.firstApplicantGender == 2}">女</c:when>
                                        <c:otherwise>未填写</c:otherwise>
                                    </c:choose>
                                </span>
                            </div>
                            <c:if test="${not empty registration.firstApplicantBirthday}"><div class="detail-item"><span class="detail-label">出生日期:</span><span class="detail-value"><fmt:formatDate value="${registration.firstApplicantBirthday}" pattern="yyyy-MM-dd"/></span></div></c:if>
                            <c:if test="${not empty registration.firstApplicantGraduationTime}"><div class="detail-item"><span class="detail-label">毕业时间:</span><span class="detail-value"><fmt:formatDate value="${registration.firstApplicantGraduationTime}" pattern="yyyy-MM-dd"/></span></div></c:if>
                            <c:if test="${param.role != 'judge' && not empty registration.firstApplicantIdCard}">
                                <div class="detail-item"><span class="detail-label">身份证号:</span><span class="detail-value">${registration.firstApplicantIdCard}</span></div>
                            </c:if>
                            <c:if test="${param.role != 'judge' && (not empty registration.firstApplicantIdCardFrontFile || not empty registration.firstApplicantIdCardBackFile)}">
                                <div class="detail-item">
                                    <span class="detail-label">身份证照片:</span>
                                    <span class="detail-value id-card-images">
                                        <c:if test="${not empty registration.firstApplicantIdCardFrontFile}">
                                            <c:set var="frontUrl" value="${registration.firstApplicantIdCardFrontFile}" />
                                            <c:if test="${fn:startsWith(frontUrl, 'http://')}">
                                                <c:set var="frontUrl" value="${fn:replace(frontUrl, 'http://', 'https://')}" />
                                            </c:if>
                                            <img src="${frontUrl}" alt="身份证正面">
                                        </c:if>
                                        <c:if test="${not empty registration.firstApplicantIdCardBackFile}">
                                            <c:set var="backUrl" value="${registration.firstApplicantIdCardBackFile}" />
                                            <c:if test="${fn:startsWith(backUrl, 'http://')}">
                                                <c:set var="backUrl" value="${fn:replace(backUrl, 'http://', 'https://')}" />
                                            </c:if>
                                            <img src="${backUrl}" alt="身份证反面">
                                        </c:if>
                                    </span>
                                </div>
                            </c:if>
                        </div>
                    </div>
                    
                    <div class="detail-section">
                        <h4 class="detail-section-header">团队成员信息</h4>
                        <div class="detail-section-body" id="membersList">
                            <p class="text-muted">暂无团队成员信息</p>
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4 class="detail-section-header">指导老师信息</h4>
                        <div class="detail-section-body" id="mentorsList">
                            <p class="text-muted">暂无指导老师信息</p>
                        </div>
                    </div>

                    <c:if test="${registration.competitionSubgroup == 'startup' || registration.competitionSubgroup == 'growth'}">
                        <div class="detail-section">
                            <h4 class="detail-section-header">公司信息</h4>
                            <div class="detail-section-body">
                                <div class="detail-item"><span class="detail-label">公司名称:</span><span class="detail-value">${registration.companyName}</span></div>
                                <c:if test="${not empty registration.companyEstablishTime}"><div class="detail-item"><span class="detail-label">成立时间:</span><span class="detail-value"><fmt:formatDate value="${registration.companyEstablishTime}" pattern="yyyy-MM-dd"/></span></div></c:if>
                                <c:if test="${not empty registration.companyAddress}"><div class="detail-item"><span class="detail-label">公司地址:</span><span class="detail-value">${registration.companyAddress}</span></div></c:if>
                                <c:if test="${not empty registration.companyBusinessLicenseFile}">
                                    <c:set var="licenseUrl" value="${registration.companyBusinessLicenseFile}" />
                                    <c:if test="${fn:startsWith(licenseUrl, 'http://')}">
                                        <c:set var="licenseUrl" value="${fn:replace(licenseUrl, 'http://', 'https://')}" />
                                    </c:if>
                                    <div class="detail-item"><span class="detail-label">营业执照:</span><span class="detail-value"><img src="${licenseUrl}" style="max-width: 200px;" class="img-thumbnail" alt="营业执照"></span></div>
                                </c:if>
                            </div>
                        </div>
                    </c:if>

                    <div class="detail-section">
                        <h4 class="detail-section-header">项目信息</h4>
                        <div class="detail-section-body">
                             <c:if test="${not empty registration.projectBrief}"><div class="detail-item"><span class="detail-label">项目简介:</span><span class="detail-value">${registration.projectBrief}</span></div></c:if>
                             <c:if test="${not empty registration.industryCompetitiveAdvantage}"><div class="detail-item"><span class="detail-label">竞争优势:</span><span class="detail-value">${registration.industryCompetitiveAdvantage}</span></div></c:if>
                             <c:if test="${not empty registration.socialBenefits}"><div class="detail-item"><span class="detail-label">社会效益:</span><span class="detail-value">${registration.socialBenefits}</span></div></c:if>
                             <c:if test="${not empty registration.teamQuality}"><div class="detail-item"><span class="detail-label">团队素质:</span><span class="detail-value">${registration.teamQuality}</span></div></c:if>
                             <c:if test="${not empty registration.financialOperation}"><div class="detail-item"><span class="detail-label">财务运营:</span><span class="detail-value">${registration.financialOperation}</span></div></c:if>
                             <c:if test="${not empty registration.marketProspect}"><div class="detail-item"><span class="detail-label">市场前景:</span><span class="detail-value">${registration.marketProspect}</span></div></c:if>
                             <c:if test="${not empty registration.productService}"><div class="detail-item"><span class="detail-label">产品服务:</span><span class="detail-value">${registration.productService}</span></div></c:if>
                             <c:if test="${not empty registration.isAgreePublic}"><div class="detail-item">
                                 <span class="detail-label">是否同意公开:</span>
                                 <span class="detail-value">
                                    <c:choose><c:when test="${registration.isAgreePublic == 1}">是</c:when><c:when test="${registration.isAgreePublic == 0}">否</c:when><c:otherwise>未填写</c:otherwise></c:choose>
                                 </span>
                             </div></c:if>
                        </div>
                    </div>
                 </div>
                 <c:if test="${param.role == 'judge'}">
                    <div class="score-button-container">
                        <button type="button" class="btn btn-lg btn-primary" onclick="openScoreDialog()">
                            <i class="fa fa-check-square-o"></i> 打分
                        </button>
                    </div>
                 </c:if>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    //强制转换HTTP为HTTPS
    function forceHttpsUrl(url) {
        if (url && url.startsWith('http://')) {
            return url.replace('http://', 'https://');
        }
        return url;
    }

    // 强制转换页面中所有HTTP链接为HTTPS
    function convertAllHttpToHttps() {
        $('iframe').each(function() {
            var src = $(this).attr('src');
            if (src) {
                var httpsSrc = forceHttpsUrl(src);
                if (httpsSrc !== src) {
                    $(this).attr('src', httpsSrc);
                }
            }
        });

        $('img').each(function() {
            var src = $(this).attr('src');
            if (src) {
                var httpsSrc = forceHttpsUrl(src);
                if (httpsSrc !== src) {
                    $(this).attr('src', httpsSrc);
                }
            }
        });
    }

    // HTML转义函数，确保安全显示
    function htmlEscape(str) {
        if (!str) return '未填写';
        return String(str)
            .replace(/&/g, '&amp;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;');
    }

    // 从URL获取参数的辅助函数
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return decodeURIComponent(r[2]); return null;
    }

    $(document).ready(function() {
        var isJudge = "${param.role}" === "judge";

        try {
            var membersDataRaw = '${registration.projectMembers}';
            if (membersDataRaw && membersDataRaw !== 'null' && membersDataRaw !== '' && membersDataRaw !== '[]') {
                var members = JSON.parse(membersDataRaw.replace(/&quot;/g, '"'));
                if (Array.isArray(members) && members.length > 0) {
                    displayMembers(members, isJudge);
                }
            }
        } catch (e) {
            console.error('解析团队成员数据失败:', e);
            $('#membersList').html('<p class="text-muted">团队成员数据格式错误</p>');
        }

        try {
            var mentorsDataRaw = '${registration.mentors}';
            if (mentorsDataRaw && mentorsDataRaw !== 'null' && mentorsDataRaw !== '' && mentorsDataRaw !== '[]') {
                var mentors = JSON.parse(mentorsDataRaw.replace(/&quot;/g, '"'));
                if (Array.isArray(mentors) && mentors.length > 0) {
                    displayMentors(mentors, isJudge);
                }
            }
        } catch (e) {
            console.error('解析指导老师数据失败:', e);
            $('#mentorsList').html('<p class="text-muted">指导老师数据格式错误</p>');
        }
    });

    function displayMembers(members, isJudge) {
        var html = '';
        members.forEach(function(member, index) {
            html += '<div class="member-card">';
            html += '<div class="member-title">团队成员' + (index + 1) + '</div>';

            html += '<div class="detail-item"><span class="detail-label">姓名:</span><span class="detail-value">' + htmlEscape(member.name) + '</span></div>';
            var genderText = member.gender == '1' ? '男' : (member.gender == '2' ? '女' : '未填写');
            html += '<div class="detail-item"><span class="detail-label">性别:</span><span class="detail-value">' + genderText + '</span></div>';
            html += '<div class="detail-item"><span class="detail-label">职位:</span><span class="detail-value">' + htmlEscape(member.position) + '</span></div>';
            html += '<div class="detail-item"><span class="detail-label">出生日期:</span><span class="detail-value">' + htmlEscape(member.birthday) + '</span></div>';
            html += '<div class="detail-item"><span class="detail-label">户籍:</span><span class="detail-value">' + htmlEscape(member.hukou) + '</span></div>';

            if (!isJudge) {
                html += '<div class="detail-item"><span class="detail-label">手机号:</span><span class="detail-value">' + htmlEscape(member.mobile || member.contact || '') + '</span></div>';
                html += '<div class="detail-item"><span class="detail-label">邮箱:</span><span class="detail-value">' + htmlEscape(member.email) + '</span></div>';
                html += '<div class="detail-item"><span class="detail-label">身份证号:</span><span class="detail-value">' + htmlEscape(member.idCard) + '</span></div>';
                if (member.idCardFront || member.idCardBack) {
                    html += '<div class="detail-item"><span class="detail-label">身份证照片:</span><span class="detail-value id-card-images">';
                    if (member.idCardFront) {
                        var frontUrl = forceHttpsUrl(member.idCardFront);
                        html += '<img src="' + htmlEscape(frontUrl) + '" alt="身份证正面">';
                    }
                    if (member.idCardBack) {
                        var backUrl = forceHttpsUrl(member.idCardBack);
                        html += ' <img src="' + htmlEscape(backUrl) + '" alt="身份证反面">';
                    }
                    html += '</span></div>';
                }
            }
            html += '</div>';
        });
        $('#membersList').html(html);
    }

    function displayMentors(mentors, isJudge) {
        var html = '';
        mentors.forEach(function(mentor, index) {
            html += '<div class="member-card">';
            html += '<div class="member-title">指导老师' + (index + 1) + '</div>';

            html += '<div class="detail-item"><span class="detail-label">姓名:</span><span class="detail-value">' + htmlEscape(mentor.name) + '</span></div>';
            html += '<div class="detail-item"><span class="detail-label">工作单位:</span><span class="detail-value">' + htmlEscape(mentor.workplace) + '</span></div>';
            html += '<div class="detail-item"><span class="detail-label">职位:</span><span class="detail-value">' + htmlEscape(mentor.position) + '</span></div>';

            if (!isJudge) {
                html += '<div class="detail-item"><span class="detail-label">手机号:</span><span class="detail-value">' + htmlEscape(mentor.mobile) + '</span></div>';
                html += '<div class="detail-item"><span class="detail-label">邮箱:</span><span class="detail-value">' + htmlEscape(mentor.email) + '</span></div>';
            }
            html += '</div>';
        });
        $('#mentorsList').html(html);
    }

    function openScoreDialog() {
        var reviewId = '${param.reviewId}';
        var projectId = '${registration.id}';
        var projectName = '${registration.participantProject}';
        var currentScore = getUrlParam('myScore'); // 动态获取最新的分数

        var html = '<div style="padding: 20px;">' +
                   '    <p style="margin-bottom: 10px;">为项目【' + projectName + '】打分 (0-100):</p>' +
                   '    <input type="number" id="scoreInput" class="layui-layer-input" value="' + (currentScore && currentScore !== 'null' ? currentScore : '') + '">' +
                   '</div>';

        top.layer.open({
            type: 1,
            title: '项目打分',
            area: ['400px', '220px'],
            content: html,
            btn: ['确定', '取消'],
            yes: function(index, layero){
                var value = layero.find('#scoreInput').val();
                var score = parseFloat(value);

                if (value === '' || isNaN(score) || score < 0 || score > 100) {
                    top.layer.msg('请输入0到100之间的有效分数！', {icon: 2});
                    return;
                }

                top.layer.close(index);
                var loadingIndex = top.layer.load(1, { shade: [0.1, '#fff'] });

                $.ajax({
                    url: '${ctx}/qqc/judgeReview/saveScore',
                    type: 'POST',
                    data: {
                        reviewId: reviewId,
                        projectId: projectId,
                        score: score
                    },
                    success: function(result) {
                        top.layer.close(loadingIndex);
                        if(result.success) {
                            top.layer.msg('打分成功！', {icon: 1, time: 1000}, function(){
                                // 刷新父页面和自身
                                refreshParentAndSelf(score);
                            });
                        } else {
                            top.layer.msg('打分失败: ' + result.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        top.layer.close(loadingIndex);
                        top.layer.msg('操作失败，请稍后重试', {icon: 2});
                    }
                });
            }
        });
    }

    function refreshParentAndSelf(newScore) {
        // 刷新父页面A (评审列表)
        if (top.refreshReviewListPage && typeof top.refreshReviewListPage === 'function') {
            top.refreshReviewListPage();
        } else if (top.mainFrame) {
            top.mainFrame.location.reload();
        }

        // 刷新本页面B (详情弹窗) 并更新分数参数
        var currentUrl = window.location.href;
        var newUrl = currentUrl.replace(/(&myScore=)[^&]*/, '$1' + newScore);
        // 如果原URL没有myScore参数，则添加它
        if (currentUrl.indexOf('myScore=') === -1) {
             if (currentUrl.indexOf('?') === -1) {
                newUrl = currentUrl + '?myScore=' + newScore;
            } else {
                newUrl = currentUrl + '&myScore=' + newScore;
            }
        }
        window.location.href = newUrl;
    }

    $(document).ready(function() {
        convertAllHttpToHttps();

        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // 元素节点
                            $(node).find('iframe, img').addBack('iframe, img').each(function() {
                                var src = $(this).attr('src');
                                if (src) {
                                    var httpsSrc = forceHttpsUrl(src);
                                    if (httpsSrc !== src) {
                                        $(this).attr('src', httpsSrc);
                                    }
                                }
                            });
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    });
</script>
</body>
</html>