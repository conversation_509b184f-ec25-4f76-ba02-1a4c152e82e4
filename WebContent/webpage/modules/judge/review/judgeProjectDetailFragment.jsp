<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>

<style type="text/css">
    .project-detail-container {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        background-color: #fff;
        padding: 5px;
        box-sizing: border-box;
        position: relative;
    }

    .detail-section {
        border: 1px solid #e7eaec;
        border-radius: 4px;
        margin-bottom: 15px;
        box-shadow: 0 1px 2px rgba(0,0,0,.05);
    }
    .detail-section-header {
        background-color: #f5f5f5;
        padding: 10px 12px;
        border-bottom: 1px solid #e7eaec;
        font-size: 15px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }
    .detail-section-body {
        padding: 12px;
    }
    .detail-item {
        display: flex;
        padding: 6px 0;
        font-size: 13px;
        border-bottom: 1px solid #f0f0f0;
        line-height: 1.4;
    }
    .detail-item:last-child {
        border-bottom: none;
    }
    .detail-label {
        flex: 0 0 100px;
        font-weight: 600;
        color: #676a6c;
        padding-right: 8px;
        font-size: 12px;
    }
    .detail-value {
        flex: 1;
        color: #333;
        word-wrap: break-word;
        word-break: break-all;
        line-height: 1.5;
    }

    .detail-value.long-text {
        max-height: 120px;
        overflow-y: auto;
        padding: 5px;
        background-color: #fafafa;
        border-radius: 3px;
        border: 1px solid #eee;
        font-size: 12px;
    }

    .project-info-section .detail-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .project-info-section .detail-label {
        flex: none;
        margin-bottom: 3px;
        font-weight: bold;
    }

    .project-info-section .detail-value {
        width: 100%;
        margin-left: 0;
    }
    .member-card {
        border: 1px solid #e7eaec;
        border-radius: 4px;
        padding: 10px;
        margin-bottom: 10px;
        background-color: #f9f9f9;
    }
    .member-title {
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 4px;
        font-size: 13px;
    }
    .id-card-images img {
        max-width: 150px;
        margin-top: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 3px;
    }
</style>

<c:if test="${empty registration}">
    <div class="alert alert-warning">项目信息不存在</div>
</c:if>

<c:if test="${not empty registration}">
    <div class="project-detail-container">
        <h3 class="text-center" style="margin-top: 0; margin-bottom: 15px; font-size: 18px;">项目详情</h3>

    <div class="detail-section">
        <h4 class="detail-section-header">基本信息</h4>
        <div class="detail-section-body">
            <div class="detail-item">
                <span class="detail-label">参赛项目:</span>
                <span class="detail-value">${registration.participantProject}</span>
            </div>

            <div class="detail-item">
                <span class="detail-label">报名途径:</span>
                <span class="detail-value">${fns:getDictLabel(registration.registrationChannel, 'registration_channel', '未填写')}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">参赛地区:</span>
                <span class="detail-value">${registration.competitionCityName} ${registration.competitionDistrictName}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">赛事分组:</span>
                <span class="detail-value">
                    ${fns:getDictLabel(registration.competitionGroup, 'competition_group', '未填写')}
                    <c:if test="${not empty registration.competitionSubgroup}">
                        - ${fns:getDictLabel(registration.competitionSubgroup, 'competition_subgroup', '')}
                    </c:if>
                </span>
            </div>
            <c:if test="${not empty registration.ruralCompetitionSubgroup}">
                <div class="detail-item">
                    <span class="detail-label">乡村振兴分组:</span>
                    <span class="detail-value">${fns:getDictLabel(registration.ruralCompetitionSubgroup, 'rural_competition_subgroup', '未填写')}</span>
                </div>
            </c:if>
            <c:if test="${not empty registration.projectField}">
                <div class="detail-item">
                    <span class="detail-label">项目领域:</span>
                    <span class="detail-value">${fns:getDictLabel(registration.projectField, 'project_field', '未填写')}</span>
                </div>
            </c:if>
        </div>
    </div>

    <div class="detail-section">
        <h4 class="detail-section-header">第一申报人信息</h4>
        <div class="detail-section-body">
            <div class="detail-item">
                <span class="detail-label">姓名:</span>
                <span class="detail-value">${registration.firstApplicantName}</span>
            </div>
            <!-- 裁判不显示敏感信息：手机号、邮箱 -->
            <c:if test="${!isJudge}">
                <div class="detail-item">
                    <span class="detail-label">手机号:</span>
                    <span class="detail-value">${registration.firstApplicantMobile}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">邮箱:</span>
                    <span class="detail-value">${registration.firstApplicantEmail}</span>
                </div>
            </c:if>

            <c:if test="${not empty registration.firstApplicantHukou}">
                <div class="detail-item">
                    <span class="detail-label">户籍:</span>
                    <span class="detail-value">${registration.firstApplicantHukou}</span>
                </div>
            </c:if>
            <c:if test="${not empty registration.firstApplicantPosition}">
                <div class="detail-item">
                    <span class="detail-label">职位:</span>
                    <span class="detail-value">${registration.firstApplicantPosition}</span>
                </div>
            </c:if>
            <div class="detail-item">
                <span class="detail-label">性别:</span>
                <span class="detail-value">
                    <c:choose>
                        <c:when test="${registration.firstApplicantGender == 1}">男</c:when>
                        <c:when test="${registration.firstApplicantGender == 2}">女</c:when>
                        <c:otherwise>未填写</c:otherwise>
                    </c:choose>
                </span>
            </div>
            <c:if test="${not empty registration.firstApplicantBirthday}">
                <div class="detail-item">
                    <span class="detail-label">出生日期:</span>
                    <span class="detail-value">
                        <fmt:formatDate value="${registration.firstApplicantBirthday}" pattern="yyyy-MM-dd"/>
                    </span>
                </div>
            </c:if>
            <c:if test="${not empty registration.firstApplicantGraduationTime}">
                <div class="detail-item">
                    <span class="detail-label">毕业时间:</span>
                    <span class="detail-value">
                        <fmt:formatDate value="${registration.firstApplicantGraduationTime}" pattern="yyyy-MM-dd"/>
                    </span>
                </div>
            </c:if>

            <!-- 裁判不显示身份证号 -->
            <c:if test="${!isJudge && not empty registration.firstApplicantIdCard}">
                <div class="detail-item">
                    <span class="detail-label">身份证号:</span>
                    <span class="detail-value">${registration.firstApplicantIdCard}</span>
                </div>
            </c:if>
            <!-- 裁判不显示身份证照片 -->
            <c:if test="${!isJudge && (not empty registration.firstApplicantIdCardFrontFile || not empty registration.firstApplicantIdCardBackFile)}">
                <div class="detail-item">
                    <span class="detail-label">身份证照片:</span>
                    <span class="detail-value id-card-images">
                        <c:if test="${not empty registration.firstApplicantIdCardFrontFile}">
                            <img src="${registration.firstApplicantIdCardFrontFile}" alt="身份证正面">
                        </c:if>
                        <c:if test="${not empty registration.firstApplicantIdCardBackFile}">
                            <img src="${registration.firstApplicantIdCardBackFile}" alt="身份证反面">
                        </c:if>
                    </span>
                </div>
            </c:if>
        </div>
    </div>

    <div class="detail-section">
        <h4 class="detail-section-header">团队成员信息</h4>
        <div class="detail-section-body" id="membersList">
            <p class="text-muted">暂无团队成员信息</p>
        </div>
    </div>

    <div class="detail-section">
        <h4 class="detail-section-header">指导老师信息</h4>
        <div class="detail-section-body" id="mentorsList">
            <p class="text-muted">暂无指导老师信息</p>
        </div>
    </div>

    <c:if test="${registration.competitionSubgroup == 'startup' || registration.competitionSubgroup == 'growth'}">
        <div class="detail-section">
            <h4 class="detail-section-header">公司信息</h4>
            <div class="detail-section-body">
                <c:if test="${not empty registration.companyName}">
                    <div class="detail-item">
                        <span class="detail-label">公司名称:</span>
                        <span class="detail-value">${registration.companyName}</span>
                    </div>
                </c:if>
                <c:if test="${not empty registration.companyEstablishTime}">
                    <div class="detail-item">
                        <span class="detail-label">成立时间:</span>
                        <span class="detail-value">
                            <fmt:formatDate value="${registration.companyEstablishTime}" pattern="yyyy-MM-dd"/>
                        </span>
                    </div>
                </c:if>
                <c:if test="${not empty registration.companyAddress}">
                    <div class="detail-item">
                        <span class="detail-label">公司地址:</span>
                        <span class="detail-value">${registration.companyAddress}</span>
                    </div>
                </c:if>
                <c:if test="${not empty registration.companyBusinessLicenseFile}">
                    <div class="detail-item">
                        <span class="detail-label">营业执照:</span>
                        <span class="detail-value">
                            <img src="${registration.companyBusinessLicenseFile}" style="max-width: 200px;" class="img-thumbnail" alt="营业执照">
                        </span>
                    </div>
                </c:if>
            </div>
        </div>
    </c:if>

    <div class="detail-section">
        <h4 class="detail-section-header">项目信息</h4>
        <div class="detail-section-body project-info-section">
            <c:if test="${not empty registration.projectBrief}">
                <div class="detail-item">
                    <span class="detail-label">项目简介:</span>
                    <span class="detail-value long-text">${registration.projectBrief}</span>
                </div>
            </c:if>
            <c:if test="${not empty registration.industryCompetitiveAdvantage}">
                <div class="detail-item">
                    <span class="detail-label">竞争优势:</span>
                    <span class="detail-value long-text">${registration.industryCompetitiveAdvantage}</span>
                </div>
            </c:if>
            <c:if test="${not empty registration.socialBenefits}">
                <div class="detail-item">
                    <span class="detail-label">社会效益:</span>
                    <span class="detail-value long-text">${registration.socialBenefits}</span>
                </div>
            </c:if>
            <c:if test="${not empty registration.teamQuality}">
                <div class="detail-item">
                    <span class="detail-label">团队素质:</span>
                    <span class="detail-value long-text">${registration.teamQuality}</span>
                </div>
            </c:if>
            <c:if test="${not empty registration.financialOperation}">
                <div class="detail-item">
                    <span class="detail-label">财务运营:</span>
                    <span class="detail-value long-text">${registration.financialOperation}</span>
                </div>
            </c:if>
            <c:if test="${not empty registration.marketProspect}">
                <div class="detail-item">
                    <span class="detail-label">市场前景:</span>
                    <span class="detail-value long-text">${registration.marketProspect}</span>
                </div>
            </c:if>
            <c:if test="${not empty registration.productService}">
                <div class="detail-item">
                    <span class="detail-label">产品服务:</span>
                    <span class="detail-value long-text">${registration.productService}</span>
                </div>
            </c:if>
        </div>
    </div>

    <!-- 填充剩余空间，防止出现紫色区域 -->
    <div style="flex: 1; background-color: #ffffff; min-height: 50px;"></div>

    </div>
</c:if>

<script type="text/javascript">
    // HTML 转义函数
    function htmlEscape(str) {
        if (!str) return '';
        return String(str)
            .replace(/&/g, '&amp;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;');
    }

    $(document).ready(function() {
        // 获取是否为裁判的标识
        var isJudge = ${isJudge};

        try {
            var membersDataRaw = '${registration.projectMembers}';
            
            if (membersDataRaw && membersDataRaw !== 'null' && membersDataRaw !== '' && membersDataRaw !== '[]') {
                membersDataRaw = membersDataRaw.replace(/&quot;/g, '"');
                
                var members = JSON.parse(membersDataRaw);
                
                if (Array.isArray(members) && members.length > 0) {
                    displayMembers(members, isJudge);
                } else {
                    $('#membersList').html('<p class="form-control-static">暂无团队成员信息</p>');
                }
            } else {
                $('#membersList').html('<p class="form-control-static">暂无团队成员信息</p>');
            }
        } catch (e) {
            console.error('解析团队成员数据出错:', e);
            $('#membersList').html('<p class="form-control-static">团队成员数据格式错误</p>');
        }

        try {
            var mentorsDataRaw = '${registration.mentors}';
            
            if (mentorsDataRaw && mentorsDataRaw !== 'null' && mentorsDataRaw !== '' && mentorsDataRaw !== '[]') {
                mentorsDataRaw = mentorsDataRaw.replace(/&quot;/g, '"');
                
                var mentors = JSON.parse(mentorsDataRaw);
                
                if (Array.isArray(mentors) && mentors.length > 0) {
                    displayMentors(mentors, isJudge);
                } else {
                    $('#mentorsList').html('<p class="form-control-static">暂无指导老师信息</p>');
                }
            } else {
                $('#mentorsList').html('<p class="form-control-static">暂无指导老师信息</p>');
            }
        } catch (e) {
            console.error('解析指导老师数据出错:', e);
            $('#mentorsList').html('<p class="form-control-static">指导老师数据格式错误</p>');
        }
    });

    function displayMembers(members, isJudge) {
        var html = '';
        members.forEach(function(member, index) {
            html += '<div class="member-card">';
            html += '<div class="member-title">团队成员' + (index + 1) + '</div>';

            html += '<div class="detail-item">';
            html += '<span class="detail-label">姓名:</span>';
            html += '<span class="detail-value">' + htmlEscape(member.name) + '</span>';
            html += '</div>';

            var genderText = '未填写';
            if (member.gender == '1' || member.gender == 1) {
                genderText = '男';
            } else if (member.gender == '2' || member.gender == 2) {
                genderText = '女';
            }
            html += '<div class="detail-item">';
            html += '<span class="detail-label">性别:</span>';
            html += '<span class="detail-value">' + genderText + '</span>';
            html += '</div>';

            if (!isJudge) {
                if (member.mobile || member.contact) {
                    html += '<div class="detail-item">';
                    html += '<span class="detail-label">手机号:</span>';
                    html += '<span class="detail-value">' + htmlEscape(member.mobile || member.contact || '') + '</span>';
                    html += '</div>';
                }

                if (member.email) {
                    html += '<div class="detail-item">';
                    html += '<span class="detail-label">邮箱:</span>';
                    html += '<span class="detail-value">' + htmlEscape(member.email) + '</span>';
                    html += '</div>';
                }

                if (member.idCard) {
                    html += '<div class="detail-item">';
                    html += '<span class="detail-label">身份证号:</span>';
                    html += '<span class="detail-value">' + htmlEscape(member.idCard) + '</span>';
                    html += '</div>';
                }
            }

            if (member.birthday) {
                html += '<div class="detail-item">';
                html += '<span class="detail-label">出生日期:</span>';
                html += '<span class="detail-value">' + htmlEscape(member.birthday) + '</span>';
                html += '</div>';
            }

            if (member.hukou) {
                html += '<div class="detail-item">';
                html += '<span class="detail-label">户籍:</span>';
                html += '<span class="detail-value">' + htmlEscape(member.hukou) + '</span>';
                html += '</div>';
            }

            if (member.position) {
                html += '<div class="detail-item">';
                html += '<span class="detail-label">职位:</span>';
                html += '<span class="detail-value">' + htmlEscape(member.position) + '</span>';
                html += '</div>';
            }

            // 如果不是裁判且有身份证照片，显示
            if (!isJudge && (member.idCardFront || member.idCardBack)) {
                html += '<div class="detail-item">';
                html += '<span class="detail-label">身份证照片:</span>';
                html += '<span class="detail-value id-card-images">';
                if (member.idCardFront) {
                    html += '<img src="' + htmlEscape(member.idCardFront) + '" alt="身份证正面">';
                }
                if (member.idCardBack) {
                    html += '<img src="' + htmlEscape(member.idCardBack) + '" alt="身份证反面">';
                }
                html += '</span>';
                html += '</div>';
            }
            html += '</div>';
        });
        $('#membersList').html(html);
    }

    function displayMentors(mentors, isJudge) {
        var html = '';
        mentors.forEach(function(mentor, index) {
            html += '<div class="member-card">';
            html += '<div class="member-title">指导老师' + (index + 1) + '</div>';

            html += '<div class="detail-item">';
            html += '<span class="detail-label">姓名:</span>';
            html += '<span class="detail-value">' + htmlEscape(mentor.name) + '</span>';
            html += '</div>';

            if (mentor.workplace) {
                html += '<div class="detail-item">';
                html += '<span class="detail-label">工作单位:</span>';
                html += '<span class="detail-value">' + htmlEscape(mentor.workplace) + '</span>';
                html += '</div>';
            }

            if (!isJudge) {
                if (mentor.mobile) {
                    html += '<div class="detail-item">';
                    html += '<span class="detail-label">手机号:</span>';
                    html += '<span class="detail-value">' + htmlEscape(mentor.mobile) + '</span>';
                    html += '</div>';
                }

                if (mentor.email) {
                    html += '<div class="detail-item">';
                    html += '<span class="detail-label">邮箱:</span>';
                    html += '<span class="detail-value">' + htmlEscape(mentor.email) + '</span>';
                    html += '</div>';
                }
            }

            html += '</div>';
        });
        $('#mentorsList').html(html);
    }
</script>
