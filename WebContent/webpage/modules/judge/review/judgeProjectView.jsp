<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<!DOCTYPE html>
<html>
<head>
    <title>裁判项目详情查看</title>
    <meta name="decorator" content="default"/>
    <style type="text/css">
        .project-container {
            display: flex;
            height: 100vh;
            min-height: 600px;
            width: 100%;
            box-sizing: border-box;
        }
        
        .project-list {
            width: 250px;
            border-right: 1px solid #e5e5e5;
            padding: 10px;
            overflow-y: auto;
            background-color: #f9f9f9;
        }
        
        .project-detail {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        
        .pdf-viewer {
            width: 45%;
            border-right: 1px solid #e5e5e5;
            padding: 8px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .pdf-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-weight: bold;
            color: #337ab7;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }

        .pdf-title-text {
            flex: 1;
            text-align: center;
        }

        .pdf-open-btn {
            background-color: #337ab7;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .pdf-open-btn:hover {
            background-color: #286090;
        }

        .pdf-open-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .pdf-content {
            flex: 1;
            overflow: hidden;
        }
        
        .project-info {
            width: 55%;
            padding: 8px;
            overflow-y: auto;
            box-sizing: border-box;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #ffffff;
            position: relative;
        }

        #projectDetailInner {
            flex: 1;
            height: 100%;
            overflow-y: auto;
            background-color: #ffffff;
        }
        
        .project-item {
            padding: 10px;
            margin-bottom: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            background-color: #fff;
            transition: all 0.3s;
        }
        
        .project-item:hover {
            background-color: #e6f3ff;
            border-color: #337ab7;
        }
        
        .project-item.active {
            background-color: #337ab7;
            color: white;
            border-color: #337ab7;
        }
        
        .project-name {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .project-score {
            font-size: 12px;
        }
        
        .score-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }
        
        .score-success {
            background-color: #5cb85c;
            color: white;
        }
        
        .score-warning {
            background-color: #f0ad4e;
            color: white;
        }
        
        .project-list-title {
            font-weight: bold;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #337ab7;
            color: #337ab7;
        }

        .floating-score-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            border-radius: 50px;
            padding: 15px 25px;
            font-size: 16px;
            font-weight: bold;
        }

        .form-section {
            background-color: #f5f5f5;
            padding: 8px 15px;
            margin: 20px 0 15px 0;
            border-left: 4px solid #337ab7;
            font-weight: bold;
            font-size: 14px;
        }
        
        .member-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #fff;
        }
        
        .member-title {
            font-weight: bold;
            color: #337ab7;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .info-row {
            margin-bottom: 8px;
            line-height: 1.6;
        }
        
        .info-label {
            font-weight: bold;
            color: #555;
        }
        
        .id-card-images img {
            max-width: 200px;
            max-height: 150px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 5px;
        }

        body {
            margin: 0;
            padding: 0;
            overflow: auto;
        }

        .project-container {
            width: 100%;
            box-sizing: border-box;
        }

        @media (min-width: 1400px) {
            .project-list {
                width: 300px;
            }
        }

        @media (max-width: 1200px) {
            .project-list {
                width: 200px;
            }
        }

        .pdf-viewer, .project-info {
            height: 100vh;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            background-color: #ffffff;
        }

        .layui-layer-content {
            overflow: visible !important;
        }
    </style>
</head>
<body class="gray-bg">

<div class="project-container">
    <!-- 左侧项目清单 -->
    <div class="project-list">
        <div class="project-list-title">项目清单</div>
        <c:forEach items="${projectList}" var="project" varStatus="status">
            <div class="project-item ${project.id == currentProject.id ? 'active' : ''}" 
                 onclick="switchProject('${project.id}')" 
                 data-project-id="${project.id}">
                <div class="project-name" title="${project.participantProject}">
                    ${fns:abbr(project.participantProject, 30)}
                </div>
                <div class="project-score">
                    <c:choose>
                        <c:when test="${not empty project.myScore}">
                            <span class="score-badge score-success">已打分: ${project.myScore}</span>
                        </c:when>
                        <c:otherwise>
                            <span class="score-badge score-warning">未打分</span>
                        </c:otherwise>
                    </c:choose>
                </div>
            </div>
        </c:forEach>
        
        <c:if test="${empty projectList}">
            <div style="text-align: center; color: #999; margin-top: 50px;">
                暂无项目
            </div>
        </c:if>
    </div>
    
    <!-- 右侧项目详情 -->
    <div class="project-detail" id="projectDetailContent">
        <c:if test="${not empty error}">
            <div class="alert alert-danger" style="width: 100%;">${error}</div>
        </c:if>
        
        <c:if test="${not empty currentProject}">
            <!-- 左侧PDF查看器 -->
            <div class="pdf-viewer">
                <div class="pdf-title">
                    <div class="pdf-title-text">📄 项目计划书</div>
                    <button type="button" id="openPdfBtn" class="pdf-open-btn" onclick="openPdfInNewWindow()" disabled>
                        <i class="fa fa-external-link"></i> 新窗口打开
                    </button>
                </div>
                <div class="pdf-content" id="pdfContainer">
                    <!-- PDF内容将在这里显示 -->
                </div>
            </div>
            
            <!-- 右侧项目信息 -->
            <div class="project-info">
                <div id="projectDetailInner">
                    <!-- 项目详情内容将通过 AJAX 加载 -->
                </div>
            </div>
        </c:if>
    </div>
</div>

<!-- 浮动打分按钮 -->
<button type="button" id="floatingScoreBtn" class="btn btn-success floating-score-btn" style="display: none;" onclick="openCurrentProjectScoreDialog()">
    <i class="fa fa-check-square-o"></i> 为此项目打分
</button>

<script type="text/javascript">
    var currentReviewId = '${reviewId}';
    var currentProjectId = '${currentProject.id}';
    var currentPdfUrl = null;

    // 全局函数：强制转换HTTP为HTTPS
    function forceHttpsUrl(url) {
        if (url && url.startsWith('http://')) {
            return url.replace('http://', 'https://');
        }
        return url;
    }

    // 定义switchProject函数
    window.switchProject = function(projectId) {
        if (projectId === currentProjectId) {
            return;
        }

        // 更新当前项目ID
        currentProjectId = projectId;

        // 更新项目列表的选中状态
        $('.project-item').removeClass('active');
        $('.project-item[data-project-id="' + projectId + '"]').addClass('active');

        loadProjectDetail(projectId);

        showFloatingScoreBtn();
    };

    function loadProjectDetail(projectId) {
        var loadingHtml = '<div style="text-align: center; padding: 50px;"><i class="fa fa-spinner fa-spin fa-2x"></i><br/>加载中...</div>';
        $('#projectDetailInner').html(loadingHtml);
        $('#pdfContainer').html('<div style="text-align: center; padding: 50px;"><i class="fa fa-spinner fa-spin fa-2x"></i><br/>加载PDF中...</div>');

        $.ajax({
            url: '${ctx}/qqc/judgeReview/getProjectDetail',
            type: 'GET',
            data: {
                projectId: projectId,
                reviewId: currentReviewId
            },
            success: function(data) {
                $('#projectDetailInner').html(data);
            },
            error: function(xhr, status, error) {
                $('#projectDetailInner').html('<div class="alert alert-danger">加载项目详情失败: ' + error + '</div>');
            }
        });

        loadProjectPdf(projectId);
    }

    function showFloatingScoreBtn() {
        if (currentProjectId && currentProjectId !== '' && currentProjectId !== 'null' &&
            currentReviewId && currentReviewId !== '' && currentReviewId !== 'null') {
            $('#floatingScoreBtn').show();
        } else {
            $('#floatingScoreBtn').hide();
        }
    }

    function loadProjectPdf(projectId) {
        // 开始加载时禁用按钮
        $('#openPdfBtn').prop('disabled', true);
        currentPdfUrl = null;

        $.ajax({
            url: '${ctx}/qqc/judgeReview/getProjectPdf',
            type: 'GET',
            data: {
                projectId: projectId
            },
            dataType: 'json',
            success: function(response) {
                var pdfUrl = null;
                if (response.success) {
                    if (response.pdfUrl) {
                        pdfUrl = response.pdfUrl;
                    } else if (response.body && response.body.pdfUrl) {
                        pdfUrl = response.body.pdfUrl;
                    }
                }

                // 强制转换HTTP为HTTPS
                pdfUrl = forceHttpsUrl(pdfUrl);

                if (pdfUrl) {
                    displayPdf(pdfUrl);
                } else {
                    var message = response.msg || '暂无项目计划书';
                    $('#pdfContainer').html('<div style="text-align: center; padding: 30px; color: #999;">' +
                        '<i class="fa fa-file-pdf-o" style="font-size: 48px; margin-bottom: 10px;"></i><br/>' +
                        message + '</div>');
                }
            },
            error: function(xhr, status, error) {
                $('#pdfContainer').html('<div style="text-align: center; padding: 30px; color: #dc3545;">' +
                    '<i class="fa fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 10px;"></i><br/>' +
                    'PDF加载失败<br/>' +
                    '<small>错误: ' + error + '</small>' +
                    '</div>');
            }
        });
    }

    function displayPdf(pdfUrl) {
        if (!pdfUrl || pdfUrl.trim() === '') {
            $('#pdfContainer').html('<div style="text-align: center; padding: 30px; color: #999;">' +
                '<i class="fa fa-file-pdf-o" style="font-size: 48px; margin-bottom: 10px;"></i><br/>' +
                '暂无项目计划书</div>');
            // 禁用新窗口打开按钮
            currentPdfUrl = null;
            $('#openPdfBtn').prop('disabled', true);
            return;
        }

        var fullPdfUrl = pdfUrl;
        if (!pdfUrl.startsWith('http') && !pdfUrl.startsWith('/')) {
            fullPdfUrl = '${ctx}/' + pdfUrl;
        } else if (pdfUrl.startsWith('/') && !pdfUrl.startsWith('${ctx}') && !pdfUrl.startsWith('http')) {
            fullPdfUrl = '${ctx}' + pdfUrl;
        }

        // 强制转换HTTP为HTTPS
        fullPdfUrl = forceHttpsUrl(fullPdfUrl);

        // 更新全局PDF URL变量
        currentPdfUrl = fullPdfUrl;

        // 显示加载状态
        $('#pdfContainer').html('<div style="text-align: center; padding: 50px;">' +
            '<i class="fa fa-spinner fa-spin fa-2x"></i><br/>' +
            '正在加载PDF...</div>');

        // 创建iframe来显示PDF
        setTimeout(function() {
            var pdfHtml = '<div style="position: relative; height: 100%; width: 100%;">' +
                '<iframe id="pdfIframe" src="' + fullPdfUrl + '" ' +
                'style="width: 100%; height: 100%; border: 1px solid #ddd; border-radius: 4px; display: block;" ' +
                'frameborder="0">' +
                '</iframe>' +
                '</div>';
            $('#pdfContainer').html(pdfHtml);

            // 启用新窗口打开按钮
            $('#openPdfBtn').prop('disabled', false);

            // 添加错误处理
            setTimeout(function() {
                var iframe = document.getElementById('pdfIframe');
                if (iframe) {
                    iframe.onload = function() {
                        $('#openPdfBtn').prop('disabled', false);
                        // PDF加载完成后调整大小
                        setTimeout(function() {
                            adjustPdfSize();
                        }, 100);
                    };
                    iframe.onerror = function() {
                        var errorHtml = '<div style="text-align: center; padding: 30px; color: #dc3545;">' +
                                       '<i class="fa fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 10px;"></i><br/>' +
                                       'PDF文件加载失败<br/>';

                        var httpsUrl = forceHttpsUrl(fullPdfUrl);
                        var isConverted = httpsUrl !== fullPdfUrl;

                        errorHtml += '<small style="color: #666; margin-top: 10px; display: block;">PDF加载失败，可能是由于安全策略限制</small>' +
                                    '<button onclick="window.open(\'' + httpsUrl + '\', \'_blank\')" class="btn btn-primary btn-sm" style="margin-top: 10px;">' +
                                    '<i class="fa fa-external-link"></i> 在新窗口中打开PDF' + (isConverted ? ' (HTTPS)' : '') +
                                    '</button>';

                        errorHtml += '</div>';
                        $('#pdfContainer').html(errorHtml);
                        $('#openPdfBtn').prop('disabled', true);
                        currentPdfUrl = null;
                    };
                }
            }, 100);
        }, 500);
    }

    function openCurrentProjectScoreDialog() {
        if (!currentProjectId || !currentReviewId) {
            top.layer.msg('请先选择一个项目', {icon: 2});
            return;
        }

        var currentProjectName = $('.project-item.active .project-name').text() || '当前项目';
        openScoreDialog(currentReviewId, currentProjectId, currentProjectName);
    }

    function openScoreDialog(reviewId, projectId, projectName) {
        var html = '<div style="padding: 20px;">' +
            '    <p style="margin-bottom: 10px;">为项目【' + projectName + '】打分 (0-100):</p>' +
            '    <input type="number" id="scoreInput" class="layui-layer-input" min="0" max="100" step="0.01">' +
            '</div>';

        top.layer.open({
            type: 1,
            title: '项目打分',
            area: ['400px', '220px'],
            content: html,
            btn: ['确定', '取消'],
            yes: function(index, layero){
                var value = layero.find('#scoreInput').val();
                var score = parseFloat(value);

                console.log('输入的分数:', value, '解析后的分数:', score);

                if (value === '' || isNaN(score) || score < 0 || score > 100) {
                    top.layer.msg('请输入0到100之间的有效分数！', {icon: 2});
                    return;
                }

                top.layer.close(index);
                var loadingIndex = top.layer.load(1, { shade: [0.1, '#fff'] });

                $.ajax({
                    url: '${ctx}/qqc/judgeReview/saveScore',
                    type: 'POST',
                    data: {
                        reviewId: reviewId,
                        projectId: projectId,
                        score: score
                    },
                    success: function(result) {
                        top.layer.close(loadingIndex);
                        if(result.success) {
                            refreshProjectList(score);

                            updateParentWindowScore(projectId, score);

                            notifyParentWindowRefresh(projectId, score);

                            top.layer.msg('打分成功！', {icon: 1, time: 1000});
                        } else {
                            top.layer.msg('打分失败: ' + result.msg, {icon: 2});
                        }
                    },
                    error: function() {
                        top.layer.close(loadingIndex);
                        top.layer.msg('操作失败，请稍后重试', {icon: 2});
                    }
                });
            }
        });
    }

    function refreshProjectList(score) {
        console.log('刷新项目列表，分数:', score);

        var scoreHtml;
        if (score !== undefined && score !== null) {
            // 显示具体分数
            scoreHtml = '<span class="score-badge score-success">已打分: ' + parseFloat(score).toFixed(2) + '</span>';
        } else {
            // 如果没有传递分数，则从服务器重新获取
            scoreHtml = '<span class="score-badge score-success">已打分</span>';

            // 异步获取最新的分数
            $.ajax({
                url: '${ctx}/qqc/judgeReview/getProjectScore',
                type: 'GET',
                data: {
                    projectId: currentProjectId,
                    reviewId: currentReviewId
                },
                success: function(response) {
                    if (response.success && response.score !== undefined) {
                        $('.project-item[data-project-id="' + currentProjectId + '"]').find('.project-score').html(
                            '<span class="score-badge score-success">已打分: ' + parseFloat(response.score).toFixed(2) + '</span>'
                        );
                    }
                },
                error: function() {
                    console.log('获取分数失败，保持当前显示');
                }
            });
        }

        $('.project-item[data-project-id="' + currentProjectId + '"]').find('.project-score').html(scoreHtml);
    }

    function updateParentWindowScore(projectId, score) {
        try {
            console.log('尝试更新父窗口分数 - projectId:', projectId, 'score:', score);

            if (top && top.$) {
                top.$('iframe').each(function() {
                    try {
                        var iframeWindow = this.contentWindow;
                        if (iframeWindow && iframeWindow.updateProjectScore && typeof iframeWindow.updateProjectScore === 'function') {
                            iframeWindow.updateProjectScore(projectId, score);
                            console.log('成功调用iframe中的updateProjectScore函数');
                        }
                    } catch (e) {
                        // 忽略跨域或其他访问错误
                    }
                });
            }

            if (parent && parent !== window && parent.updateProjectScore && typeof parent.updateProjectScore === 'function') {
                parent.updateProjectScore(projectId, score);
                console.log('成功调用parent的updateProjectScore函数');
            }

            if (window.opener && window.opener.updateProjectScore && typeof window.opener.updateProjectScore === 'function') {
                window.opener.updateProjectScore(projectId, score);
                console.log('成功调用opener的updateProjectScore函数');
            }

        } catch (e) {
            console.error('更新父窗口分数失败:', e);
        }
    }

    function notifyParentWindowRefresh(projectId, score) {
        try {
            var message = {
                type: 'SCORE_UPDATED',
                projectId: projectId,
                score: score,
                timestamp: new Date().getTime()
            };

            // 发送给 parent 窗口
            if (parent && parent !== window) {
                parent.postMessage(message, '*');
            }

            // 发送给 opener 窗口
            if (window.opener) {
                window.opener.postMessage(message, '*');
            }

            // 发送给 top 窗口
            if (top && top !== window) {
                top.postMessage(message, '*');
            }

            if (top && top.$) {
                top.$('iframe').each(function() {
                    try {
                        var iframeWindow = this.contentWindow;
                        if (iframeWindow && iframeWindow.postMessage) {
                            iframeWindow.postMessage(message, '*');
                        }
                    } catch (e) {
                        // 忽略跨域错误
                    }
                });
            }

            console.log('已发送刷新通知给父窗口');

        } catch (e) {
            console.error('发送刷新通知失败:', e);
        }
    }

    function openPdfInNewWindow() {
        if (!currentPdfUrl) {
            top.layer.msg('暂无PDF文件可打开', {icon: 2});
            return;
        }


        var urlToOpen = forceHttpsUrl(currentPdfUrl);

        window.open(urlToOpen, '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');
    }

    function adjustLayout() {
        var windowHeight = $(window).height();
        var windowWidth = $(window).width();


        $('.project-container').css({
            'height': windowHeight + 'px',
            'width': '100%',
            'background-color': '#ffffff'
        });


        $('.pdf-viewer').css({
            'width': '',
            'height': windowHeight + 'px'
        });

        $('.project-info').css({
            'width': '',
            'height': windowHeight + 'px'
        });

        if (windowWidth > 1400) {
            $('.project-list').css('width', '300px');
        } else if (windowWidth > 1200) {
            $('.project-list').css('width', '250px');
        } else if (windowWidth > 900) {
            $('.project-list').css('width', '200px');
        } else {
            $('.project-list').css('width', '180px');
        }

        // 确保PDF内容正确填充
        setTimeout(function() {
            adjustPdfSize();
        }, 100);
    }

    function adjustPdfSize() {
        var pdfContainer = $('.pdf-content');
        var pdfIframe = $('#pdfIframe');

        if (pdfContainer.length > 0 && pdfIframe.length > 0) {
            // 获取容器的实际尺寸
            var containerHeight = pdfContainer.height();
            var containerWidth = pdfContainer.width();

            console.log('PDF容器尺寸:', containerWidth, 'x', containerHeight);

            // 确保iframe完全填充容器
            pdfIframe.css({
                'width': '100%',
                'height': '100%',
                'min-height': containerHeight + 'px',
                'min-width': containerWidth + 'px',
                'display': 'block',
                'border': '1px solid #ddd',
                'border-radius': '4px',
                'background-color': '#ffffff'
            });
        }
    }

    // 额外的调试函数
    function debugLayout() {
        console.log('=== 布局调试信息 ===');
        console.log('窗口尺寸:', $(window).width(), 'x', $(window).height());
        console.log('容器尺寸:', $('.project-container').width(), 'x', $('.project-container').height());
        console.log('项目列表尺寸:', $('.project-list').width(), 'x', $('.project-list').height());
        console.log('项目详情尺寸:', $('.project-detail').width(), 'x', $('.project-detail').height());
        console.log('PDF查看器尺寸:', $('.pdf-viewer').width(), 'x', $('.pdf-viewer').height());
        console.log('项目信息尺寸:', $('.project-info').width(), 'x', $('.project-info').height());

        // 检查是否有溢出
        $('.project-container, .project-detail, .pdf-viewer, .project-info').each(function() {
            var $this = $(this);
            var scrollWidth = this.scrollWidth;
            var clientWidth = this.clientWidth;
            if (scrollWidth > clientWidth) {
                console.warn('元素宽度溢出:', $this.attr('class'), '滚动宽度:', scrollWidth, '客户端宽度:', clientWidth);
            }
        });
    }

    $(document).ready(function() {

        // 强制设置页面样式，防止紫色区域
        $('html, body').css({
            'width': '100%',
            'height': '100%',
            'margin': '0',
            'padding': '0',
            'background-color': '#ffffff',
            'overflow': 'hidden'
        });

        $('.project-container').css({
            'width': '100%',
            'height': '100vh',
            'background-color': '#ffffff',
            'display': 'flex'
        });

        if (currentProjectId && currentProjectId !== '' && currentProjectId !== 'null') {
            loadProjectDetail(currentProjectId);
            showFloatingScoreBtn();
        } else {
            $('#projectDetailInner').html('<div style="text-align: center; padding: 50px; color: #999;">请从左侧选择一个项目查看详情</div>');
            $('#pdfContainer').html('<div style="text-align: center; padding: 50px; color: #999;">请先选择项目</div>');
        }

        setTimeout(function() {
            if ($('#floatingScoreBtn').length > 0) {
                $('#floatingScoreBtn').show();
            }
        }, 1000);

        setTimeout(function() {
            adjustLayout();
        }, 200);

        $(window).on('resize', function() {
            clearTimeout(window.resizeTimer);
            window.resizeTimer = setTimeout(function() {
                adjustLayout();
            }, 100);
        });

        if (window.ResizeObserver) {
            var resizeObserver = new ResizeObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.target.classList.contains('project-info') ||
                        entry.target.classList.contains('pdf-viewer')) {
                        console.log('检测到尺寸变化:', entry.target.className,
                            entry.contentRect.width, 'x', entry.contentRect.height);
                    }
                });
            });

            if ($('.project-info').length > 0) {
                resizeObserver.observe($('.project-info')[0]);
            }
            if ($('.pdf-viewer').length > 0) {
                resizeObserver.observe($('.pdf-viewer')[0]);
            }
        }

        console.log('页面初始化完成');
    });
</script>

</body>
</html>
