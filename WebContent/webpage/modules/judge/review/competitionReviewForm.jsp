<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
    <title>新增评审</title>
    <meta name="decorator" content="default"/>
    <script type="text/javascript">
       function pageRegistrations(pageNo, pageSize) {
           $('input[name="registrationPage.pageNo"]').val(pageNo);
           $('input[name="registrationPage.pageSize"]').val(pageSize);
           $("#addActivityForm").attr("action", "${ctx}/judge/review/form");
           $("#addActivityForm").submit();
       }

       function pageJudges(pageNo, pageSize) {
           $('input[name="judgePage.pageNo"]').val(pageNo);
           $('input[name="judgePage.pageSize"]').val(pageSize);
           $("#addActivityForm").attr("action", "${ctx}/judge/review/form");
           $("#addActivityForm").submit();
       }

       $(document).ready(function() {
          // 更新勾选逻辑
          $(document).on('change', '.project-checkbox', function() {
              var projectId = $(this).val();
              if ($(this).is(':checked')) {
                  // 如果勾选，且隐藏域中不存在，则添加
                  if ($('#selectedProjectsContainer input[value="' + projectId + '"]').length === 0) {
                      $('#selectedProjectsContainer').append('<input type="hidden" name="projectIds" value="' + projectId + '"/>');
                  }
              } else {
                  // 如果取消勾选，则从隐藏域中移除
                  $('#selectedProjectsContainer input[value="' + projectId + '"]').remove();
              }
              var selectedCount = $('#selectedProjectsContainer input').length;
              $('input[name="reviewProjects"]').val('已选择 ' + selectedCount + ' 项');
          });

          $(document).on('change', '.judge-checkbox', function() {
              var judgeId = $(this).val();
              if ($(this).is(':checked')) {
                  if ($('#selectedJudgesContainer input[value="' + judgeId + '"]').length === 0) {
                      $('#selectedJudgesContainer').append('<input type="hidden" name="judgeIds" value="' + judgeId + '"/>');
                  }
              } else {
                  $('#selectedJudgesContainer input[value="' + judgeId + '"]').remove();
              }
              var selectedCount = $('#selectedJudgesContainer input').length;
              $('input[name="reviewJudges"]').val('已选择 ' + selectedCount + ' 人');
          });

          $('#selectAllProjects').on('change', function() {
              var isChecked = $(this).is(':checked');
              $('#projectsTable tbody .project-checkbox').each(function() {
                  $(this).prop('checked', isChecked).trigger('change');
              });
          });

          $('#selectAllJudges').on('change', function() {
              var isChecked = $(this).is(':checked');
              $('#judgesTable tbody .judge-checkbox').each(function() {
                  $(this).prop('checked', isChecked).trigger('change');
              });
          });

          // 表单提交
          $("#btnSubmit").click(function(){
             var reviewName = $('input[name="reviewName"]').val();
             if (!reviewName) {
                top.layer.msg('请输入评审名称', {icon: 2});
                return;
             }

             var selectedProjects = $('.project-checkbox:checked').length;
             if (selectedProjects === 0) {
                top.layer.msg('请至少选择一个评审项目', {icon: 2});
                return;
             }

             var selectedJudges = $('.judge-checkbox:checked').length;
             if (selectedJudges === 0) {
                top.layer.msg('请至少选择一位评审裁判', {icon: 2});
                return;
             }

             $("#addActivityForm").attr("action", "${ctx}/judge/review/save");

             var loadingIndex = top.layer.msg('正在保存，请稍候...', {
                icon: 16,
                shade: 0.3,
                time: 0
             });

             $.ajax({
                url: $('#addActivityForm').attr('action'),
                type: 'POST',
                data: $('#addActivityForm').serialize(),
                dataType: 'json',
                success: function(data) {
                   top.layer.close(loadingIndex);
                   if (data.success) {
                      top.layer.msg(data.msg || '保存成功!', {
                          icon: 1,
                          time: 1000
                      }, function() {
                          // 先获取当前iframe的index
                          var index = parent.layer.getFrameIndex(window.name);

                          // 调用顶层窗口的刷新函数，绕过跨域问题
                          if (top.refreshReviewListPage && typeof top.refreshReviewListPage === 'function') {
                            top.refreshReviewListPage();
                          } else {
                            console.error("【弹窗C】: 错误！在顶层窗口中没有找到 refreshReviewListPage 函数！");
                          }

                          // 再执行关闭
                          parent.layer.close(index);
                      });
                   } else {
                      top.layer.msg(data.msg || '保存失败', {icon: 2});
                   }
                },
                error: function(xhr, status, error) {
                   top.layer.close(loadingIndex);
                   console.error('保存失败:', error);
                   top.layer.msg('系统错误，请联系管理员', {icon: 2});
                }
             });
          });

          $("#btnCancel").click(function() {
             var index = parent.layer.getFrameIndex(window.name);
             parent.layer.close(index);
          });

          // 初始化已选择项目和裁判的数量
          var initialProjectCount = $('#selectedProjectsContainer input').length;
          $('input[name="reviewProjects"]').val('已选择 ' + initialProjectCount + ' 项');
          var initialJudgeCount = $('#selectedJudgesContainer input').length;
          $('input[name="reviewJudges"]').val('已选择 ' + initialJudgeCount + ' 人');

       });

       function filterProjects() {
           $("#addActivityForm input[name='pageNo']").val(1); // 每次筛选都重置到第一页
           submitFormForRefresh();
       }

       function searchJudges() {
           $("#addActivityForm input[name='pageNo']").val(1);
           submitFormForRefresh();
       }

       function resetJudgeSearch() {
            $('input[name="name"]').val('');
            searchJudges();
       }

       function resetFilters() {
           $('input[name="participantProject"]').val('');
           $('select[name="competitionGroup"]').val('');
           $('select[name="status"]').val('');
           $("#addActivityForm input[name='pageNo']").val(1);
           filterProjects();
       }

       function submitFormForRefresh() {
           var form = $("#addActivityForm");
           var originalAction = form.attr("action");
           form.attr("action", "${ctx}/judge/review/form?isRefresh=true");
           form.submit();
           form.attr("action", originalAction); // 恢复原始action
       }
    </script>
</head>
<body>
<div style="padding: 20px;">
    <form:form id="addActivityForm" modelAttribute="competitionReview" action="${ctx}/judge/review/save" method="post" class="form-horizontal">
       <form:hidden path="id"/>
       <input type="hidden" name="competitionId" value="${param.competitionId}"/>
       
       <div id="selectedProjectsContainer" style="display: none;">
          <c:forEach var="pid" items="${competitionReview.projectIds}">
             <input type="hidden" name="projectIds" value="${pid}"/>
          </c:forEach>
       </div>
       <div id="selectedJudgesContainer" style="display: none;">
          <c:forEach var="jid" items="${competitionReview.judgeIds}">
             <input type="hidden" name="judgeIds" value="${jid}"/>
          </c:forEach>
       </div>

       <input type="hidden" name="registrationPage.pageNo" value="${registrationPage.pageNo}"/>
       <input type="hidden" name="registrationPage.pageSize" value="${registrationPage.pageSize}"/>
       <input type="hidden" name="judgePage.pageNo" value="${judgePage.pageNo}"/>
       <input type="hidden" name="judgePage.pageSize" value="${judgePage.pageSize}"/>

       <div class="form-group">
          <label class="col-sm-2 control-label"><span style="color:red;">*</span> 评审名称：</label>
          <div class="col-sm-10">
             <form:input path="reviewName" htmlEscape="false" class="form-control required"/>
          </div>
       </div>
       <!-- 只有团省委才能看到复审评审选项 -->
       <c:if test="${isCqcAdmin}">
       <div class="form-group">
           <label class="col-sm-2 control-label">评审类型：</label>
           <div class="col-sm-10">
               <div class="checkbox">
                   <label>
                       <form:checkbox path="reviewType" value="1" onchange="submitFormForRefresh()"/> 是否为复审评审 (勾选后将只显示初审晋级的项目)
                   </label>
               </div>
           </div>
       </div>
       </c:if>
       <div class="form-group">
          <label class="col-sm-2 control-label"><span style="color:red;">*</span> 评审项目：</label>
          <div class="col-sm-10">
             <input type="text" name="reviewProjects" class="form-control" readonly="true" placeholder="已选择 0 项"/>
          </div>
       </div>
       <div class="form-group">
          <label class="col-sm-2 control-label">搜索项目：</label>
          <div class="col-sm-4">
             <input type="text" name="participantProject" class="form-control" placeholder="输入项目名称进行搜索" value="${participantProject}"/>
          </div>
          <div class="col-sm-4">
            <button type="button" class="btn btn-primary" onclick="filterProjects();">搜索</button>
            <button type="button" class="btn btn-default" onclick="resetFilters();" style="margin-left: 5px;">重置</button>
          </div>
       </div>
       <div class="form-group">
          <label class="col-sm-2 control-label">赛事分组：</label>
          <div class="col-sm-4">
             <select name="competitionGroup" class="form-control" onchange="filterProjects();">
                <option value="">全部</option>
                <c:forEach items="${fns:getDictList('competition_group')}" var="dict">
                   <option value="${dict.value}" <c:if test="${dict.value == competitionGroup}">selected</c:if>>${dict.label}</option>
                </c:forEach>
             </select>
          </div>
<%--          <label class="col-sm-2 control-label">项目状态：</label>--%>
<%--          <div class="col-sm-4">--%>
<%--             <select name="status" class="form-control" onchange="filterProjects();">--%>
<%--                <option value="">全部</option>--%>
<%--                <c:forEach items="${fns:getDictList('competition_registration_status')}" var="dict">--%>
<%--                   <option value="${dict.value}" <c:if test="${dict.value == projectStatus}">selected</c:if>>${dict.label}</option>--%>
<%--                </c:forEach>--%>
<%--             </select>--%>
<%--          </div>--%>
          <input type="hidden" name="status" value="3"/>
       </div>

       <div class="table-responsive" style="max-height: 200px; overflow-y: auto; margin-top:15px;">
          <table id="projectsTable" class="table table-bordered table-striped">
             <thead>
             <tr>
                <th style="width: 5%;"><input type="checkbox" id="selectAllProjects"/></th>
                <th>项目名称</th>
                <th>报名途径</th>
                <th>参赛地区</th>
                <th>赛事分组</th>
                <th>行业分类</th>
                <th>状态</th>
             </tr>
             </thead>
             <tbody>
             <c:forEach items="${registrationPage.list}" var="registration">
                <tr>
                   <td>
                       <c:set var="isProjectChecked" value="false"/>
                       <c:forEach var="selectedId" items="${competitionReview.projectIds}">
                           <c:if test="${selectedId == registration.id}">
                               <c:set var="isProjectChecked" value="true"/>
                           </c:if>
                       </c:forEach>
                       <input type="checkbox" class="project-checkbox" name="projectIds" value="${registration.id}" <c:if test="${isProjectChecked}">checked="checked"</c:if>/>
                   </td>
                   <td>${registration.participantProject}</td>
                   <td>${fns:getDictLabel(registration.registrationChannel, 'registration_channel', '')}</td>
                   <td>${registration.competitionCityName} - ${registration.competitionDistrictName}</td>
                   <td>${fns:getDictLabel(registration.competitionGroup, 'competition_group', '')}</td>
                   <td>${fns:getDictLabel(registration.projectField, 'project_field', '')}</td>
                   <td>
                      <!-- 使用正确的字典类型来显示状态 -->
                      <c:set var="statusLabel" value="${fns:getDictLabel(registration.status, 'competition_registration_status', '未知状态')}" />
                      <c:choose>
                         <c:when test="${registration.status == 2}">
                            <span class="label label-danger">${statusLabel}</span>
                         </c:when>
                         <c:when test="${registration.status == -1}">
                            <span class="label label-danger">${statusLabel}</span>
                         </c:when>
                         <c:when test="${registration.status == 0}">
                            <span class="label label-default">${statusLabel}</span>
                         </c:when>
                         <c:when test="${registration.status == 1}">
                            <span class="label label-warning">${statusLabel}</span>
                         </c:when>
                         <c:when test="${registration.status == 3}">
                            <span class="label label-success">${statusLabel}</span>
                         </c:when>
                         <c:when test="${registration.status == 4}">
                            <span class="label label-info">${statusLabel}</span>
                         </c:when>
                         <c:when test="${registration.status == 5}">
                            <span class="label label-primary">${statusLabel}</span>
                         </c:when>
                         <c:when test="${registration.status == 6}">
                            <span class="label label-primary">${statusLabel}</span>
                         </c:when>
                         <c:otherwise>
                            <span class="label label-default">${statusLabel}</span>
                         </c:otherwise>
                      </c:choose>
                   </td>
                </tr>
             </c:forEach>
             </tbody>
          </table>
          <div id="projectPagination">
            <table:page page="${registrationPage}" ></table:page>
          </div>
       </div>

       <div class="form-group" style="margin-top: 15px;">
          <label class="col-sm-2 control-label"><span style="color:red;">*</span> 评审裁判：</label>
          <div class="col-sm-10">
             <input type="text" name="reviewJudges" class="form-control" readonly="true" placeholder="已选择 0 项"/>
          </div>
       </div>
       <div class="form-group">
          <label class="col-sm-2 control-label">搜索裁判：</label>
          <div class="col-sm-8">
             <input type="text" name="name" class="form-control" placeholder="输入裁判名称进行搜索" value="${judgeName}"/>
          </div>
          <div class="col-sm-2">
             <button type="button" class="btn btn-primary" onclick="searchJudges();">搜索</button>
             <button type="button" class="btn btn-default" onclick="resetJudgeSearch();" style="margin-left: 5px;">重置</button>
          </div>
       </div>
       <div class="table-responsive" style="max-height: 200px; overflow-y: auto; margin-top:15px;">
          <table id="judgesTable" class="table table-bordered table-striped">
             <thead>
             <tr>
                <th style="width: 5%;"><input type="checkbox" id="selectAllJudges"/></th>
                <th>裁判姓名</th>
                <th>所属单位</th>
                <th>联系电话</th>
             </tr>
             </thead>
             <tbody>
             <c:forEach items="${judgePage.list}" var="judge">
                <tr>
                    <td>
                        <c:set var="isJudgeChecked" value="false"/>
                        <c:forEach var="selectedId" items="${competitionReview.judgeIds}">
                            <c:if test="${selectedId == judge.id}">
                                <c:set var="isJudgeChecked" value="true"/>
                            </c:if>
                        </c:forEach>
                        <input type="checkbox" class="judge-checkbox" name="judgeIds" value="${judge.id}" <c:if test="${isJudgeChecked}">checked="checked"</c:if>/>
                    </td>
                   <td>${judge.name}</td>
                   <td>${judge.city}</td>
                   <td>${judge.phone}</td>
                </tr>
             </c:forEach>
             </tbody>
          </table>
          <div id="judgePagination">
            <table:page page="${judgePage}"></table:page>
          </div>
       </div>
    </form:form>
    <div class="form-actions" style="padding: 10px 20px 0; text-align: right;">
       <input id="btnSubmit" class="btn btn-primary" type="button" value="保 存"/>&nbsp;
       <input id="btnCancel" class="btn" type="button" value="返 回" onclick="history.go(-1)">
    </div>
</div>
</body>
</html>