<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
  <title>评审管理</title>
  <meta name="decorator" content="default"/>
  <style type="text/css">
    .direct-content {
      padding: 15px;
    }
    .form-section {
      margin: 20px 0 15px;
      padding-bottom: 8px;
    }
    .page-title {
      margin-bottom: 20px;
      font-size: 18px;
      font-weight: bold;
      text-align: center;
    }
    .table-responsive {
      margin-top: 15px;
    }
    .pagination-wrapper {
      margin-top: 15px;
      text-align: center;
    }
    .review-header {
      overflow: hidden;
      margin-bottom: 20px;
    }
    .review-header h3 {
      text-align: left;
      float: left;
      margin: 0;
    }
    .review-header .header-buttons {
      float: right;
    }
  </style>
  <script type="text/javascript">
    function refreshReviews() {
        location.reload();
    }
    
    // 分页处理函数
    function page(n, s) {
        $("#pageNo").val(n);
        $("#pageSize").val(s);
        $("#searchForm").submit();
        return false;
    }
  
    $(document).ready(function() {
      // 添加评审活动按钮点击事件
      $('#addActivityBtn').click(function() {
        console.log('添加评审活动按钮被点击');

        var competitionId = "${param.competitionId}";
        if (!competitionId) {
          console.error('competitionId为空');
          top.layer.msg('赛事ID不能为空', {icon: 2});
          return;
        }

        var url = '${ctx}/judge/review/form?competitionId=' + competitionId;
        console.log('准备打开URL:', url);

        // 检查layer是否存在
        if (typeof top.layer === 'undefined') {
          console.error('layer组件未加载');
          alert('弹窗组件未加载，请刷新页面后重试');
          return;
        }

        top.layer.open({
          type: 2,
          title: '新增评审',
          area: ['1000px', '750px'],
          content: url,
          maxmin: true,
          success: function(layero, index) {
            console.log('弹窗打开成功');
          },
          cancel: function(index) {
            console.log('弹窗被取消');
          },
          end: function() {
            console.log('弹窗关闭');
            // 刷新页面或重新加载评审列表
            location.reload();
          }
        });
      });
    });
  </script>
</head>
<body>
<div class="direct-content">
  <h3 class="page-title">评审管理</h3>

  <form id="searchForm" action="${ctx}/judge/review/list" method="post" style="display: none;">
    <input id="pageNo" name="pageNo" type="hidden" value="${reviewList.pageNo}"/>
    <input id="pageSize" name="pageSize" type="hidden" value="${reviewList.pageSize}"/>
    <input name="competitionId" type="hidden" value="${param.competitionId}"/>
  </form>

  <!-- 评审活动列表 -->
  <div id="reviewActivities" class="form-section" style="margin-top:0;">
    <div class="review-header">
      <h3>评审活动</h3>
      <div class="header-buttons">
        <button id="addActivityBtn" class="btn btn-primary">
          <i class="fa fa-plus"></i> 添加评审活动
        </button>
      </div>
    </div>

    <div>
      <table id="reviewTable" class="table table-striped table-bordered table-hover table-condensed dataTables-example dataTable no-footer">
        <thead>
        <tr>
          <th>评审名称</th>
          <th>项目数</th>
          <th>裁判数</th>
          <th>操作</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach items="${reviewList.list}" var="review">
          <tr>
            <td>${review.reviewName}</td>
            <td>${review.projectCount}</td>
            <td>${review.judgeCount}</td>
            <td style="white-space: nowrap;">
              <shiro:hasPermission name="qqc:review:export">
                <a href="#" onclick="exportReviewScores('${review.id}')">导出评分</a>
              </shiro:hasPermission>
              <shiro:hasPermission name="qqc:review:edit">
                | <a href="#" onclick="openCustomDialog('编辑评审', '${ctx}/judge/review/form?id=${review.id}','1000px', '750px')">编辑评审</a>
              </shiro:hasPermission>
              <shiro:hasPermission name="qqc:review:view">
                | <a href="#" onclick="openDialogView('${review.reviewName} - 评审项目', '${ctx}/judge/review/projects?reviewId=${review.id}','1200px', '800px')">查看评审结果</a>
              </shiro:hasPermission>
              <shiro:hasPermission name="qqc:review:view">
                | <a href="#" onclick="openDialogView('评审裁判', '${ctx}/judge/review/judges?reviewId=${review.id}','800px', '600px')">查看裁判</a>
              </shiro:hasPermission>
              <shiro:hasPermission name="qqc:review:del">
                | <a href="#" onclick="confirmDelete('${review.id}', '${review.reviewName}')">删除</a>
              </shiro:hasPermission>
            </td>
          </tr>
        </c:forEach>

        <!-- 如果没有数据，显示提示信息 -->
        <c:if test="${empty reviewList.list}">
          <tr>
            <td colspan="4" class="text-center">
              <div style="padding: 20px; color: #999;">
                <i class="fa fa-info-circle"></i> 暂无评审活动，点击"添加评审活动"按钮创建新的评审活动
              </div>
            </td>
          </tr>
        </c:if>
        </tbody>
      </table>

      <div class="pagination-wrapper">
        <table:page page="${reviewList}"></table:page>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  function openCustomDialog(title, url, width, height) {
      top.layer.open({
          type: 2,
          area: [width, height],
          title: title,
          maxmin: true,
          content: url,
      });
  }

  top.refreshReviewListPage = function() {
    console.log("【弹窗B -> top】: 顶层刷新函数被调用！准备刷新...");
    location.reload();
  }

  function refreshReviewList() {
    console.log("【弹窗B】: refreshReviewList 函数被成功调用！准备刷新...");
    location.reload();
  }

  console.log("【弹窗B】: 评审列表页面(父页面)加载完毕。 refreshReviewList 函数已定义。 window object is:", window);

  function confirmDelete(id, reviewName) {
    top.layer.confirm('确认要删除评审活动"' + reviewName + '"吗？<br/><span style="color:red;">注意：删除后，所有相关的项目和裁判关联都会被一并删除！</span>', {
      icon: 3,
      title: '删除确认',
      btn: ['确定删除', '取消']
    }, function(index) {
      top.layer.close(index);
      var loadingIndex = top.layer.load(1, { shade: [0.1, '#fff'] });

      $.ajax({
        url: '${ctx}/judge/review/delete',
        type: 'POST',
        data: { id: id },
        dataType: 'json',
        success: function(result) {
          top.layer.close(loadingIndex);
          if(result.success) {
            top.layer.msg(result.msg || '删除成功', {icon: 1, time: 1000}, function() {
              location.reload();
            });
          } else {
            top.layer.msg(result.msg || '删除失败', {icon: 2});
          }
        },
        error: function() {
          top.layer.close(loadingIndex);
          top.layer.msg('删除失败，请稍后重试', {icon: 2});
        }
      });
    });
  }

  // 导出评审评分
  function exportReviewScores(reviewId) {
    if (!reviewId) {
      top.layer.msg('评审ID不能为空', {icon: 2});
      return;
    }

    var loadingIndex = top.layer.msg('正在准备导出文件，请稍候...', { icon: 16, shade: 0.3, time: 0 });

    var form = $("<form>");
    form.attr('style', 'display:none');
    form.attr('target', '');
    form.attr('method', 'post');
    form.attr('action', "${ctx}/judge/review/exportScores");

    var input = $('<input>');
    input.attr('type', 'hidden');
    input.attr('name', 'reviewId');
    input.attr('value', reviewId);

    $('body').append(form);
    form.append(input);

    form.submit();
    form.remove();

    setTimeout(function() {
        top.layer.close(loadingIndex);
    }, 3000);
  }
</script>
</body>
</html>