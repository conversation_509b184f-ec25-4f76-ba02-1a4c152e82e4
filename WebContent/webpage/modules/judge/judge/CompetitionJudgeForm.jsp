<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp" %>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="decorator" content="default"/>
  <title>裁判管理</title>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
  <div class="row">
    <div class="col-sm-12 animated fadeInRight">
      <div class="mail-box">
        <div class="mail-body">
          <form:form enctype="multipart/form-data" id="inputForm" modelAttribute="competitionJudge"
                     action="${ctx}/qqc/judge/save" method="post" class="form-horizontal">
            <form:hidden path="id"/>

            <%-- 开始两列布局 --%>
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="col-sm-4 control-label"><font color="red">*</font>姓名：</label>
                  <div class="col-sm-8">
                    <form:input path="name" htmlEscape="false" maxlength="100" class="form-control required"/>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-4 control-label">性别：</label>
                  <div class="col-sm-8">
                    <form:select path="gender" class="form-control">
                      <form:option value="" label="--请选择--"/>
                      <form:option value="男" label="男"/>
                      <form:option value="女" label="女"/>
                    </form:select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-4 control-label"><font color="red">*</font>手机号：</label>
                  <div class="col-sm-8">
                    <form:input path="phone" htmlEscape="false" maxlength="200" class="form-control required"/>
                    <span id="phoneMsg" class="help-block"></span>
                  </div>
                </div>

                <!-- 照片上传 -->
                <div class="form-group">
                  <label class="col-sm-4 control-label">照片：</label>
                  <div class="col-sm-8">
                    <input type="file" id="photoFile" class="form-control" accept=".jpg,.jpeg,.png,.gif" />
                    <form:hidden path="photo" id="photo"/>
                    <div id="photoPreview" class="mt-2 ${empty competitionJudge.photo ? 'hide' : ''}">
                      <img id="photoPreviewImage" src="${competitionJudge.photo}" class="img-thumbnail" style="max-height: 100px;" />
                      <button type="button" class="btn btn-xs btn-danger mt-1" id="removePhoto">
                        <i class="fa fa-times"></i> 移除照片
                      </button>
                    </div>
                    <span class="help-block">支持 jpg、jpeg、png、gif 格式，文件大小不超过5MB</span>
                  </div>
                </div>
              </div>

              <div class="col-sm-6">
                <c:if test="${!isTuanwei}">
                <div class="form-group">
                  <label class="col-sm-4 control-label"><font color="red">*</font>省：</label>
                  <div class="col-sm-8">
                    <select id="province" name="province" class="form-control required">
                      <option value="">请选择省份</option>
                      <option value="浙江省" ${competitionJudge.province == '浙江省' ? 'selected' : ''}>浙江省</option>
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-4 control-label"><font color="red">*</font>市：</label>
                  <div class="col-sm-8">
                    <select id="city" name="city" class="form-control required">
                      <option value="">请先选择省份</option>
                    </select>
                  </div>
                </div>
                </c:if>
<%--                <c:if test="${isTuanweij}">--%>
<%--                  <input type="hidden" id="province" name="province" value="${competitionJudge.province}">--%>
<%--                  <input type="hidden" id="city" name="city" value="${competitionJudge.city}">--%>
<%--                </c:if>--%>
                <c:if test="${isTuanwei}">
                  <form:hidden path="province"/>
                  <form:hidden path="city"/>
                </c:if>
                <div class="form-group">
                  <label class="col-sm-4 control-label"><font color="red">*</font>区/县：</label>
                  <div class="col-sm-8">
                    <select id="area" name="area" class="form-control required">
                      <option value="">请先选择市</option>
                    </select>
                  </div>
                </div>

                <div class="form-group">
                  <label class="col-sm-4 control-label">简介：</label>
                  <div class="col-sm-8">
                    <form:textarea path="introduction" htmlEscape="false" rows="4" maxlength="2000" class="form-control"/>
                    <span class="help-block">最多输入2000个字符</span>
                  </div>
                </div>
              </div>
            </div>

            <%-- 备注信息（占满整行） --%>
            <div class="form-group">
              <label class="col-sm-2 control-label">备注信息：</label>
              <div class="col-sm-9">
                <form:textarea path="remarks" htmlEscape="false" rows="3" maxlength="255" class="form-control"/>
                <span class="help-block">最多输入255个字符</span>
              </div>
            </div>

            <%-- 底部按钮 --%>
<%--            <div class="form-group">--%>
<%--              <div class="col-sm-4 col-sm-offset-4">--%>
<%--                <shiro:hasPermission name="qqc:judge:edit">--%>
<%--                  <button type="button" id="btnSubmit" class="btn btn-primary" onclick="doSubmit()">--%>
<%--                    <i class="fa fa-save"></i> 保存--%>
<%--                  </button>--%>
<%--                </shiro:hasPermission>--%>
<%--                <button type="button" class="btn btn-default" onclick="jp.close()" style="margin-left: 10px;">--%>
<%--                  <i class="fa fa-reply"></i> 取消--%>
<%--                </button>--%>
<%--              </div>--%>
<%--            </div>--%>
          </form:form>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  var validateForm;

  $(document).ready(function () {
    // 照片上传函数
    function uploadPhoto(file) {
      if (!file) return;

      if (!file.type.match('image.*')) {
        top.layer.alert('请选择图片文件', {icon: 2});
        return;
      }
      if (file.size > 5 * 1024 * 1024) { // 5MB 限制
        top.layer.alert('图片大小不能超过5MB', {icon: 2});
        return;
      }

      var loadIndex = top.layer.load(1, {shade: [0.3, '#000']});
      var formData = new FormData();
      formData.append('file', file);
      formData.append('fileType', 'JUDGE_PHOTO');

      $.ajax({
        url: '${ctx}/qqc/judge/uploadFile',
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        success: function(response) {
          top.layer.close(loadIndex);
          if (response.success) {
            var ossUrl = response.ossUrl || (response.body && response.body.ossUrl);
            if (ossUrl) {
              $("#photo").val(ossUrl);
              $("#photoPreviewImage").attr('src', ossUrl);
              $("#photoPreview").removeClass('hide');
              top.layer.msg('照片上传成功', {icon: 1});
            } else {
              top.layer.alert('上传成功，但未能获取到图片地址', {icon: 2});
            }
          } else {
            top.layer.alert(response.msg || '上传失败', {icon: 2});
          }
        },
        error: function() {
          top.layer.close(loadIndex);
          top.layer.alert('上传请求失败', {icon: 2});
        }
      });
    }

    // 绑定照片上传事件
    $("#photoFile").on('change', function() {
      var file = this.files[0];
      if (file) {
        uploadPhoto(file);
      }
    });

    // 移除照片
    $("#removePhoto").on('click', function() {
      $("#photo").val('');
      $("#photoFile").val('');
      $("#photoPreview").addClass('hide');
    });

    // 浙江省地区数据
    var zhejiangRegions = {
      "浙江省": {
        "杭州市": ["上城区", "拱墅区", "西湖区", "滨江区", "余杭区", "临平区", "钱塘区", "富阳区", "临安区", "桐庐县", "淳安县", "建德市"],
        "宁波市": ["海曙区", "江北区", "北仑区", "镇海区", "鄞州区", "奉化区", "象山县", "宁海县", "余姚市", "慈溪市"],
        "温州市": ["鹿城区", "龙湾区", "瓯海区", "洞头区", "永嘉县", "平阳县", "苍南县", "文成县", "泰顺县", "瑞安市", "乐清市", "龙港市"],
        "嘉兴市": ["南湖区", "秀洲区", "嘉善县", "海盐县", "海宁市", "平湖市", "桐乡市"],
        "湖州市": ["吴兴区", "南浔区", "德清县", "长兴县", "安吉县"],
        "绍兴市": ["越城区", "柯桥区", "上虞区", "新昌县", "诸暨市", "嵊州市"],
        "金华市": ["婺城区", "金东区", "武义县", "浦江县", "磐安县", "兰溪市", "义乌市", "东阳市", "永康市"],
        "衢州市": ["柯城区", "衢江区", "常山县", "开化县", "龙游县", "江山市"],
        "舟山市": ["定海区", "普陀区", "岱山县", "嵊泗县"],
        "台州市": ["椒江区", "黄岩区", "路桥区", "玉环市", "三门县", "天台县", "仙居县", "温岭市", "临海市"],
        "丽水市": ["莲都区", "青田县", "缙云县", "遂昌县", "松阳县", "云和县", "庆元县", "景宁畲族自治县", "龙泉市"]
      }
    };

    function populateAreas(province, city, selectedArea) {
      var areaSelect = $("#area");
      areaSelect.empty().append('<option value="">请选择区/县</option>');

      if (province && city && zhejiangRegions[province] && zhejiangRegions[province][city]) {
        var areas = zhejiangRegions[province][city];
        areas.forEach(function(area) {
          areaSelect.append('<option value="' + area + '">' + area + '</option>');
        });
        if (selectedArea) {
          areaSelect.val(selectedArea);
        }
      }
    }

    $("#province").on('change', function() {
      var province = $(this).val();
      var citySelect = $("#city");
      var areaSelect = $("#area");

      citySelect.empty().append('<option value="">请选择市</option>');
      areaSelect.empty().append('<option value="">请先选择市</option>');

      if (province && zhejiangRegions[province]) {
        var cities = Object.keys(zhejiangRegions[province]);
        cities.forEach(function(city) {
          citySelect.append('<option value="' + city + '">' + city + '</option>');
        });
      }
    });

    // 城市选择变化时更新区县选项
    $("#city").on('change', function() {
      var province = $("#province").val();
      var city = $(this).val();
      populateAreas(province, city, null);
    });
    var isTuanwei = "${isTuanwei}" === "true";
    var isEditMode = "${competitionJudge.id}" !== "";

    if (isTuanwei) {
      var province = "${competitionJudge.province}";
      var city = "${competitionJudge.city}";
      var area = "${competitionJudge.area}";
      populateAreas(province, city, area);
    } else {
      if (isEditMode) {
        var province = "${competitionJudge.province}";
        var city = "${competitionJudge.city}";
        var area = "${competitionJudge.area}";

        if (province) {
          $("#province").val(province).trigger('change');
          setTimeout(function() {
            if (city) {
              $("#city").val(city).trigger('change');
              setTimeout(function() {
                if (area) {
                  $("#area").val(area);
                }
              }, 100);
            }
          }, 100);
        }
      }
    }
    $("#phone").blur(function(){
      var phone = $(this).val();
      var id = $("#id").val();
      var phoneMsg = $("#phoneMsg");
      var phoneRegex = /^\d{11}$/;

      if(phone === ''){
        phoneMsg.html("");
        $("#btnSubmit").attr("disabled", false);
        return;
      }

      if(!phoneRegex.test(phone)){
        phoneMsg.html("<span style='color:red'>手机号格式错误</span>");
        $("#btnSubmit").attr("disabled", true);
        return;
      }

      phoneMsg.html(""); // 格式正确，先清除消息
      $.get("${ctx}/qqc/judge/checkPhone", {phone: phone, id: id}, function(data){
        if(data == 'false'){
          phoneMsg.html("<span style='color:red'>手机号已存在</span>");
          $("#btnSubmit").attr("disabled", true);
        } else {
          phoneMsg.html(""); // 手机号可用，不显示任何消息
          $("#btnSubmit").attr("disabled", false);
        }
      });
    });

    // 初始化表单验证
    validateForm = $("#inputForm").validate({
      submitHandler: function (form) {
        loading('正在提交，请稍等...');

        $.ajax({
          url: '${ctx}/qqc/judge/save',
          type: 'POST',
          data: $(form).serialize(),
          dataType: 'json',
          success: function(data) {
            if(data.success) {
              closeTip();
              try {
                var activeTab = top.getActiveTab();
                if (activeTab && typeof activeTab.attr === 'function') {
                  var frameName = activeTab.attr("name");
                  top.frames[frameName].location.reload();
                } else {
                  parent.location.reload();
                }
              } catch (e) {
                parent.location.reload();
              }
              var index = parent.layer.getFrameIndex(window.name);
              parent.layer.close(index);
              top.layer.msg(data.msg || '保存成功!', {icon: 1, time: 1500});
            } else {
              // 检查是否是软删除冲突
              if (data.code === 'PHONE_DELETED_CONFLICT') {
                // 软删除冲突时关闭加载状态
                closeTip();
                // 显示确认对话框
                var confirmMsg = data.msg + '\n原记录：' + (data.deletedJudgeName || '未知');
                top.layer.confirm(confirmMsg, {
                  icon: 3,
                  title: '确认覆盖',
                  btn: ['覆盖', '取消']
                }, function(index) {
                  // 确认覆盖，使用覆盖保存接口
                  top.layer.close(index);
                  saveWithOverride(form);
                }, function(index) {
                  top.layer.close(index);
                });
              } else {
                closeTip();
                top.layer.alert(data.msg || "保存失败", {icon: 2});
              }
            }
          },
          error: function() {
            top.layer.closeAll('loading');
            top.layer.alert("系统错误，请联系管理员", {icon: 2});
          }
        });
      },
      errorContainer: "#messageBox",
      errorPlacement: function(error, element) {
        $("#messageBox").text("输入有误，请先更正。");
        if (element.is(":checkbox") || element.is(":radio") || element.parent().is(".input-append")) {
          error.appendTo(element.parent().parent());
        } else {
          error.insertAfter(element);
        }
      }
    });
  });

  function doSubmit() {
    $("#inputForm").submit();
  }
  
  // 覆盖保存已删除记录
  function saveWithOverride(form) {
    loading('正在覆盖保存，请稍等...');
    
    $.ajax({
      url: '${ctx}/qqc/judge/saveWithOverride',
      type: 'POST',
      data: $(form).serialize(),
      dataType: 'json',
      success: function(data) {
        if(data.success) {
          top.layer.closeAll('loading');
          try {
            var activeTab = top.getActiveTab();
            if (activeTab && typeof activeTab.attr === 'function') {
              var frameName = activeTab.attr("name");
              top.frames[frameName].location.reload();
            } else {
              parent.location.reload();
            }
          } catch (e) {
            parent.location.reload();
          }
          var index = parent.layer.getFrameIndex(window.name);
          parent.layer.close(index);
          top.layer.msg(data.msg || '覆盖保存成功!', {icon: 1, time: 1500});
        } else {
          top.layer.closeAll('loading');
          top.layer.alert(data.msg || "覆盖保存失败", {icon: 2});
        }
      },
      error: function() {
        top.layer.closeAll('loading');
        top.layer.alert("系统错误，请联系管理员", {icon: 2});
      }
    });
  }
</script>
</body>
</html>