<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
  <title>裁判列表</title>
  <meta name="decorator" content="default"/>
  <style type="text/css">
  </style>
  <script type="text/javascript">
    $(document).ready(function() {

    });
  </script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
  <div class="ibox">
    <div class="ibox-title">
      <h5>裁判信息 </h5>
      <div class="ibox-tools">
        <a class="collapse-link">
          <i class="fa fa-chevron-up"></i>
        </a>
        <a class="dropdown-toggle" data-toggle="dropdown" href="form_basic.html#">
          <i class="fa fa-wrench"></i>
        </a>
        <ul class="dropdown-menu dropdown-user">
          <li><a href="#">选项1</a>
          </li>
          <li><a href="#">选项2</a>
          </li>
        </ul>
        <a class="close-link">
          <i class="fa fa-times"></i>
        </a>
      </div>
    </div>

    <div class="ibox-content">
      <sys:message content="${message}"/>

      <!-- 查询条件 -->
      <div class="row">
        <div class="col-sm-12">
          <form:form id="searchForm" modelAttribute="competitionJudge" action="" method="post" class="form-inline">
            <input id="pageNo" name="pageNo" type="hidden" value="${page.pageNo}"/>
            <input id="pageSize" name="pageSize" type="hidden" value="${page.pageSize}"/>
            <div class="form-group">
              <span>姓名：&nbsp;</span>
              <form:input path="name" cssStyle="cursor: pointer" htmlEscape="false" maxlength="100" class="form-control input-sm"/>
              <span>手机号：&nbsp;</span>
              <form:input path="phone" cssStyle="cursor: pointer" htmlEscape="false" maxlength="200" class="form-control input-sm"/>
              <span>性别：</span>
              <form:select path="gender" class="form-control m-b">
                <form:option value="" label="--请选择--"/>
                <form:option value="男" label="男"/>
                <form:option value="女" label="女"/>
              </form:select>
            </div>
          </form:form>
          <br/>
        </div>
      </div>


      <!-- 工具栏 -->
      <div class="row">
        <div class="col-sm-12">
          <div class="pull-left">
            <shiro:hasPermission name="qqc:judge:add">
              <table:addRow width="1200px" height="700px" url="${ctx}/qqc/judge/form" title="裁判"></table:addRow><!-- 增加按钮 -->
            </shiro:hasPermission>
            <shiro:hasPermission name="qqc:judge:del">
              <table:delRow url="${ctx}/qqc/judge/deleteAll" id="judgeTable"></table:delRow><!-- 删除按钮 -->
            </shiro:hasPermission>
          </div>
          <div class="pull-right">
            <button class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="search()" ><i class="fa fa-search"></i> 查询</button>
            <button class="btn btn-primary btn-rounded btn-outline btn-sm " onclick="reset()" ><i class="fa fa-refresh"></i> 重置</button>
          </div>
        </div>
      </div>

      <table id="judgeTable" class="table table-striped table-bordered table-hover table-condensed dataTables-example dataTable no-footer">
        <thead>
        <tr>
          <th> <input type="checkbox" class="i-checks"></th>
          <th>照片</th>
          <th>姓名</th>
          <th>手机号</th>
          <th>简介</th>
          <th>创建时间</th>
          <th>更新时间</th>
          <th>操作</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach items="${page.list}" var="judge">
          <tr>
            <td> <input type="checkbox" id="${judge.id}" class="i-checks"></td>
            <td>
              <c:if test="${not empty judge.photo}">
                <img src="${judge.photo}" style="width:30px;height:30px;" />
              </c:if>
              <c:if test="${empty judge.photo}">
                <span class="text-muted">无</span>
              </c:if>
            </td>
            <td><a href="#" onclick="openDialogView('查看裁判', '${ctx}/qqc/judge/form?id=${judge.id}','1200px', '700px')">
                ${fns:abbr(judge.name,20)}
            </a></td>
            <td>
                ${judge.phone}
            </td>
            <td>
              <c:choose>
                <c:when test="${fn:length(judge.introduction) > 50}">
                  ${fn:substring(judge.introduction, 0, 50)}...
                </c:when>
                <c:otherwise>
                  ${judge.introduction}
                </c:otherwise>
              </c:choose>
            </td>
            <td>
              <fmt:formatDate value="${judge.createDate}" pattern="yyyy-MM-dd HH:mm" />
            </td>
            <td>
              <fmt:formatDate value="${judge.updateDate}" pattern="yyyy-MM-dd HH:mm" />
            </td>
            <td>
              <shiro:hasPermission name="qqc:judge:view">
                <a title="查看裁判" href="#" onclick="openDialogView('查看裁判', '${ctx}/qqc/judge/form?id=${judge.id}','1200px', '700px')" class="btn btn-info btn-xs" >查看</a>
              </shiro:hasPermission>
              <shiro:hasPermission name="qqc:judge:edit">
                <a title="修改裁判" href="#" onclick="openDialog('修改裁判', '${ctx}/qqc/judge/form?id=${judge.id}','1200px', '700px')" class="btn btn-success btn-xs" >修改</a>
              </shiro:hasPermission>
              <shiro:hasPermission name="qqc:judge:del">
                <a title="删除裁判" href="${ctx}/qqc/judge/delete?id=${judge.id}" onclick="return confirmx('确认要删除该裁判吗？', this.href)" class="btn btn-danger btn-xs">删除</a>
              </shiro:hasPermission>
            </td>
          </tr>
        </c:forEach>
        </tbody>
      </table>
      <!-- 分页代码 -->
      <table:page page="${page}"></table:page>
      <br/>
      <br/>
    </div>
  </div>
</div>
</body>
</html>