<%@ page contentType="text/html;charset=UTF-8" %>
<%@ include file="/webpage/include/taglib.jsp"%>
<html>
<head>
	<title>赛事报名详情列表</title>
	<meta name="decorator" content="default"/>
	<meta name="ctx" content="${ctx}"/>
	<style type="text/css">
		.direct-content {
			padding: 15px;
		}
		.layui-layer-content .form-horizontal .form-group {
			margin-left: 0 !important;
			margin-right: 0 !important;
		}
		.audit-form .form-group {
			margin-bottom: 15px;
		}
		.audit-form .control-label {
			padding-top: 7px;
			margin-bottom: 0;
			text-align: right;
		}
		.reject-reason {
			display: none;
		}
		.page-title {
			margin-bottom: 20px;
			font-size: 18px;
			font-weight: bold;
			text-align: center;
		}
		.search-area {
			margin-bottom: 15px;
		}
		.table-responsive {
			margin-top: 15px;
			overflow-x: hidden;
		}
		#auditDialog {
			overflow-x: hidden;
			width: 100%;
			box-sizing: border-box;
		}
	</style>
	<script type="text/javascript">
		// 确保变量在全局作用域
		var ctx = '${ctx}';
		
		$(document).ready(function() {
			console.log('页面加载完成，ctx:', ctx);
			
			// 检查必要的组件
			if (typeof layer === 'undefined') {
				console.error('Layer组件未加载');
			}
			
			// 检查审核弹窗HTML
			if ($('#auditDialog').length === 0) {
				console.error('审核弹窗HTML不存在');
			}
		});

		// 自定义搜索函数
		function search() {
			// 确保表单中包含competitionId参数
			if (!$("#competitionId").val() && $("#hdId").val()) {
				$("#competitionId").val($("#hdId").val());
			}
			$("#searchForm").submit();
		}

		// 删除确认函数 - 改进版本
		function confirmDelete(id, projectName) {
			console.log('删除确认被调用，ID:', id, '项目名称:', projectName);
			
			// 参数验证
			if (!id || id === 'undefined' || id === 'null') {
				console.error('删除ID无效:', id);
				layer.msg('删除ID无效', {icon: 2});
				return false;
			}
			
			if (!projectName) {
				projectName = '该项目';
			}
			
			layer.confirm('确认要删除项目 "' + projectName + '" 吗？', {
				icon: 3,
				title: '删除确认',
				btn: ['确定', '取消']
			}, function(index) {
				layer.close(index);
				
				// 显示加载中
				var loadingIndex = layer.load(1, {
					shade: [0.1, '#fff']
				});

				// 使用AJAX提交删除请求
				$.ajax({
					url: ctx + '/qqc/registration/delete',
					type: 'POST',
					data: {
						id: id,
						hdId: $("#competitionId").val() || $("#hdId").val()
					},
					success: function(result) {
						layer.close(loadingIndex);
						console.log('删除结果:', result);
						
						if(result && result.success) {
							layer.msg('删除成功', {icon: 1});
							// 刷新当前页面
							setTimeout(function() {
								location.reload();
							}, 1000);
						} else {
							layer.msg('删除失败: ' + (result.msg || '未知错误'), {icon: 2});
						}
					},
					error: function(xhr, status, error) {
						layer.close(loadingIndex);
						console.error('删除请求失败:', {xhr, status, error});
						layer.msg('删除失败，请稍后重试', {icon: 2});
					}
				});
			}, function(index) {
				// 用户点击取消，关闭确认框
				layer.close(index);
			});
		}

		// 打开审核弹窗 - 改进版本
		function openAuditDialog(id, projectName) {
			console.log('审核弹窗被调用，ID:', id, '项目名称:', projectName);
			
			// 参数验证
			if (!id || id === 'undefined' || id === 'null') {
				console.error('审核ID无效:', id);
				layer.msg('审核ID无效', {icon: 2});
				return false;
			}
			
			if (!projectName) {
				projectName = '未知项目';
			}
			
			// 检查审核弹窗HTML是否存在
			var auditDialogHtml = $('#auditDialog').html();
			if (!auditDialogHtml || auditDialogHtml.trim() === '') {
				console.error('审核弹窗HTML不存在或为空');
				layer.msg('审核弹窗组件缺失，请刷新页面重试', {icon: 2});
				return false;
			}
			
			$('#registrationId').val(id);

			// 打开弹窗
			var index = layer.open({
				type: 1,
				title: '审核报名',
				area: ['900px', '600px'],
				shadeClose: false,
				scrollbar: false,
				content: auditDialogHtml,

				success: function(layero, index){
					console.log('弹窗打开成功');
					
					layero.find('.form-group').css({
						'margin-left': '0',
						'margin-right': '0'
					});
					
					// layero 是当前弹窗的 jQuery 对象
					var form = layero.find('form'); // 找到弹窗内的表单

					form.find('#projectName').text(projectName);

					// 绑定状态变化事件
					form.find('input[name="status"]').off('change').on('change', function() {
						if($(this).val() == '-1') { // -1表示退回
							form.find('.reject-reason').show();
						} else {
							form.find('.reject-reason').hide();
						}
					});
					
					// 初始隐藏退回理由
					form.find('.reject-reason').hide();
				},

				btn: ['确定', '取消'],

				// 使用 yes 回调函数处理确定按钮的逻辑
				yes: function(index, layero) {
					console.log('点击确定按钮');
					
					var auditStatus = layero.find('input[name="status"]:checked').val();
					var rejectReason = layero.find('#rejectReason').val();

					console.log('审核状态:', auditStatus, '退回理由:', rejectReason);

					// 执行验证
					if(!auditStatus) {
						layer.msg('请选择审核结果', {icon: 2});
						return false; // 阻止弹窗关闭
					}

					if(auditStatus == '-1' && (!rejectReason || rejectReason.trim() === '')) {
						layer.msg('请填写退回理由', {icon: 2});
						return false; // 阻止弹窗关闭
					}

					// 验证通过，手动关闭弹窗
					layer.close(index);

					// 显示加载中
					var loadingIndex = layer.load(1, {
						shade: [0.1, '#fff']
					});

					// 使用AJAX提交表单
					$.ajax({
						url: ctx + '/qqc/registration/audit',
						type: 'POST',
						data: {
							id: id,
							status: auditStatus,
							rejectReason: rejectReason
						},
						success: function(result) {
							layer.close(loadingIndex);
							console.log('审核结果:', result);
							
							if(result && result.success) {
								layer.msg('审核操作成功', {icon: 1});
								setTimeout(function() {
									location.reload();
								}, 1000);
							} else {
								layer.msg('审核操作失败: ' + (result.msg || '未知错误'), {icon: 2});
							}
						},
						error: function(xhr, status, error) {
							layer.close(loadingIndex);
							console.error('审核请求失败:', {xhr, status, error});
							layer.msg('审核操作失败，请稍后重试', {icon: 2});
						}
					});
				},
				cancel: function(index) {
					console.log('取消审核');
					// 取消按钮默认就会关闭弹窗，无需额外代码
				}
			});
		}

		// 退赛确认函数 - 改进版本
		function confirmWithdraw(id, projectName) {
			console.log('退赛确认被调用，ID:', id, '项目名称:', projectName);
			
			// 参数验证
			if (!id || id === 'undefined' || id === 'null') {
				console.error('退赛ID无效:', id);
				layer.msg('退赛ID无效', {icon: 2});
				return false;
			}
			
			if (!projectName) {
				projectName = '该项目';
			}
			
			layer.confirm('确认要为项目 "' + projectName + '" 申请退赛吗？', {
				icon: 3,
				title: '退赛确认',
				btn: ['确定', '取消']
			}, function(index) {
				layer.close(index);
				
				// 显示加载中
				var loadingIndex = layer.load(1, {
					shade: [0.1, '#fff']
				});

				// 使用AJAX提交退赛请求
				$.ajax({
					url: ctx + '/qqc/registration/withdraw',
					type: 'POST',
					data: {
						id: id,
						withdrawReason: '退赛' // 设置默认退赛理由
					},
					success: function(result) {
						layer.close(loadingIndex);
						console.log('退赛结果:', result);
						
						if(result && result.success) {
							layer.msg('退赛申请成功', {icon: 1});
							setTimeout(function() {
								location.reload();
							}, 1000);
						} else {
							layer.msg('退赛申请失败: ' + (result.msg || '未知错误'), {icon: 2});
						}
					},
					error: function(xhr, status, error) {
						layer.close(loadingIndex);
						console.error('退赛请求失败:', {xhr, status, error});
						layer.msg('退赛申请失败，请稍后重试', {icon: 2});
					}
				});
			}, function(index) {
				// 用户点击取消，关闭确认框
				layer.close(index);
			});
		}
	</script>
</head>
<body>
<div class="wrapper wrapper-content">
	<div class="ibox">
		<div class="ibox-title">
			<h5>报名详情列表</h5>
		</div>
		<div class="ibox-content">
			<!-- 搜索表单 -->
			<form:form id="searchForm" modelAttribute="registration" action="${ctx}/qqc/competition/registrationList" method="post" class="form-horizontal">
				<input type="hidden" id="competitionId" name="competitionId" value="${param.competitionId}"/>
				<input type="hidden" id="hdId" name="hdId" value="${param.hdId}"/>

				<div class="form-group">
					<label class="col-sm-2 control-label">参赛项目：</label>
					<div class="col-sm-3">
						<form:input path="participantProject" htmlEscape="false" class="form-control" placeholder="请输入参赛项目名称"/>
					</div>
					<label class="col-sm-2 control-label">审核状态：</label>
					<div class="col-sm-3">
						<form:select path="status" class="form-control">
							<form:option value="" label="全部"/>
							<form:option value="0" label="草稿"/>
							<form:option value="1" label="待审核"/>
							<form:option value="3" label="审核通过"/>
							<form:option value="-1" label="已退回"/>
							<form:option value="2" label="退赛"/>
						</form:select>
					</div>
					<div class="col-sm-2">
						<button type="button" onclick="search()" class="btn btn-primary">搜索</button>
						<button type="button" onclick="resetForm()" class="btn btn-default">重置</button>
					</div>
				</div>
			</form:form>

			<!-- 报名详情列表 -->
			<div class="table-responsive">
				<table id="registrationTable" class="table table-striped table-bordered table-hover table-condensed dataTables-example dataTable no-footer">
					<thead>
					<tr>
						<th>参赛项目</th>
						<th>报名途径</th>
						<th>参赛地区</th>
						<th>赛事分组</th>
						<th>第一申报人</th>
						<th>联系方式</th>
						<th>公司名称</th>
						<th>项目简介</th>
						<th>审核状态</th>
						<th>操作</th>
					</tr>
					</thead>
					<tbody>
					<c:forEach items="${page.list}" var="registration">
						<tr data-id="${registration.id}">
							<td title="${registration.participantProject}">${fns:abbr(registration.participantProject, 20)}</td>
							<td>${fns:getDictLabel(registration.registrationChannel, 'registration_channel', '')}</td>
							<td>${registration.competitionCityName} ${registration.competitionDistrictName}</td>
							<td>${fns:getDictLabel(registration.competitionGroup, 'competition_group', '')}</td>
							<td>${registration.firstApplicantName}</td>
							<td>${registration.firstApplicantMobile}</td>
							<td title="${registration.companyName}">${fns:abbr(registration.companyName, 15)}</td>
							<td title="${registration.projectBrief}">${fns:abbr(registration.projectBrief, 30)}</td>
							<td>
								<c:set var="statusLabel" value="${fns:getDictLabel(registration.status, 'registration_status', '未知状态')}" />
								<c:choose>
									<c:when test="${registration.status == 3}">
										<span class="label label-success">${statusLabel}</span>
									</c:when>
									<c:when test="${registration.status == 1}">
										<span class="label label-warning">${statusLabel}</span>
									</c:when>
									<c:when test="${registration.status == -1}">
										<span class="label label-danger">${statusLabel}</span>
									</c:when>
									<c:when test="${registration.status == 2}">
										<span class="label label-info">${statusLabel}</span>
									</c:when>
									<c:when test="${registration.status == 0}">
										<span class="label label-primary">${statusLabel}</span>
									</c:when>
									<c:otherwise>
										<span class="label label-default">${statusLabel}</span>
									</c:otherwise>
								</c:choose>
							</td>
							<td>
								<shiro:hasPermission name="qqc:registration:view">
									<a title="查看详情" href="#" onclick="openDialogView('报名详情', '${ctx}/qqc/registration/view?id=${registration.id}','900px', '500px')" class="btn btn-info btn-xs" >查看</a>
								</shiro:hasPermission>
								<shiro:hasPermission name="qqc:registration:edit">
									<a title="编辑报名" href="#" onclick="openDialog('编辑报名信息', '${ctx}/qqc/registration/form?id=${registration.id}','900px', '500px')" class="btn btn-success btn-xs" >编辑</a>
								</shiro:hasPermission>
								<shiro:hasPermission name="qqc:registration:del">
									<a title="删除报名" href="javascript:void(0);" onclick="confirmDelete('${registration.id}', '${fns:escapeHtml(registration.participantProject)}')" class="btn btn-danger btn-xs">删除</a>
								</shiro:hasPermission>
								<shiro:hasPermission name="qqc:registration:edit">
									<a title="审核报名" href="javascript:void(0);" onclick="openAuditDialog('${registration.id}', '${fns:escapeHtml(registration.participantProject)}')" class="btn btn-primary btn-xs" >审核</a>
								</shiro:hasPermission>
								<!-- 只有在"待审核"(1)和"草稿"(0)状态下显示 -->
								<shiro:hasPermission name="qqc:registration:edit">
									<c:if test="${registration.status == 0 || registration.status == 1}">
										<a title="退赛" href="javascript:void(0);" onclick="confirmWithdraw('${registration.id}', '${fns:escapeHtml(registration.participantProject)}')" class="btn btn-warning btn-xs" >退赛</a>
									</c:if>
								</shiro:hasPermission>
							</td>
						</tr>
					</c:forEach>
					</tbody>
				</table>
			</div>

			<!-- 分页代码 -->
			<table:page page="${page}"></table:page>
		</div>
	</div>
</div>

<!-- 审核弹窗 -->
<div id="auditDialog" style="display: none; padding: 20px;">
	<form id="auditForm" class="form-horizontal audit-form">
		<input type="hidden" id="registrationId" name="id">
		<div class="form-group">
			<label class="col-sm-3 control-label">项目名称：</label>
			<div class="col-sm-9">
				<p class="form-control-static" id="projectName"></p>
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-3 control-label">审核结果：</label>
			<div class="col-sm-9">
				<div class="radio">
					<label>
						<input type="radio" name="status" value="3"> 通过
					</label>
				</div>
				<div class="radio">
					<label>
						<input type="radio" name="status" value="-1"> 退回
					</label>
				</div>
			</div>
		</div>
		<div class="form-group reject-reason" style="display: none;">
			<label class="col-sm-3 control-label">退回理由：</label>
			<div class="col-sm-9">
				<textarea id="rejectReason" name="rejectReason" class="form-control" rows="5" placeholder="请填写退回理由"></textarea>
			</div>
		</div>
	</form>
</div>

<script type="text/javascript">
	// 重置表单
	function resetForm() {
		$('#searchForm')[0].reset();
		search();
	}
</script>
</body>
</html>
