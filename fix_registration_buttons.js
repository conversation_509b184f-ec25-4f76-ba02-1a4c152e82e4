// 报名详情按钮修复脚本
// 在浏览器控制台中运行此脚本来修复常见问题

console.log("=== 开始修复报名详情按钮问题 ===");

// 1. 确保jQuery和Layer已加载
function ensureLibrariesLoaded() {
    if (typeof $ === 'undefined') {
        console.error("jQuery未加载，请检查页面引用");
        return false;
    }
    if (typeof layer === 'undefined') {
        console.error("Layer组件未加载，请检查页面引用");
        return false;
    }
    return true;
}

// 2. 重新定义审核弹窗函数（如果原函数有问题）
function fixOpenAuditDialog() {
    window.openAuditDialog = function(id, projectName) {
        console.log("调用审核弹窗，ID:", id, "项目名称:", projectName);
        
        // 检查参数
        if (!id || !projectName) {
            console.error("参数不完整:", {id, projectName});
            layer.msg('参数错误', {icon: 2});
            return;
        }
        
        // 设置隐藏字段
        $('#registrationId').val(id);
        
        // 检查审核弹窗HTML是否存在
        const auditDialogHtml = $('#auditDialog').html();
        if (!auditDialogHtml) {
            console.error("审核弹窗HTML不存在");
            layer.msg('审核弹窗组件缺失', {icon: 2});
            return;
        }
        
        // 打开弹窗
        var index = layer.open({
            type: 1,
            title: '审核报名',
            area: ['900px', '600px'],
            shadeClose: false,
            scrollbar: false,
            content: auditDialogHtml,
            
            success: function(layero, index) {
                console.log("弹窗打开成功");
                
                // 设置样式
                layero.find('.form-group').css({
                    'margin-left': '0',
                    'margin-right': '0'
                });
                
                // 找到表单
                var form = layero.find('form');
                
                // 设置项目名称
                form.find('#projectName').text(projectName);
                
                // 绑定状态变化事件
                form.find('input[name="status"]').change(function() {
                    if($(this).val() == '-1') {
                        form.find('.reject-reason').show();
                    } else {
                        form.find('.reject-reason').hide();
                    }
                });
                
                // 初始隐藏退回理由
                form.find('.reject-reason').hide();
            },
            
            btn: ['确定', '取消'],
            
            yes: function(index, layero) {
                console.log("点击确定按钮");
                
                var auditStatus = layero.find('input[name="status"]:checked').val();
                var rejectReason = layero.find('#rejectReason').val();
                
                console.log("审核状态:", auditStatus, "退回理由:", rejectReason);
                
                // 验证
                if(!auditStatus) {
                    layer.msg('请选择审核结果', {icon: 2});
                    return false;
                }
                
                if(auditStatus == '-1' && !rejectReason) {
                    layer.msg('请填写退回理由', {icon: 2});
                    return false;
                }
                
                // 关闭弹窗
                layer.close(index);
                
                // 显示加载
                var loadingIndex = layer.load(1, {
                    shade: [0.1, '#fff']
                });
                
                // 提交审核
                $.ajax({
                    url: ctx + '/qqc/registration/audit',
                    type: 'POST',
                    data: {
                        id: id,
                        status: auditStatus,
                        rejectReason: rejectReason
                    },
                    success: function(result) {
                        layer.close(loadingIndex);
                        console.log("审核结果:", result);
                        
                        if(result.success) {
                            layer.msg('审核操作成功', {icon: 1});
                            location.reload();
                        } else {
                            layer.msg('审核操作失败: ' + result.msg, {icon: 2});
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.close(loadingIndex);
                        console.error("审核请求失败:", {xhr, status, error});
                        layer.msg('审核操作失败，请稍后重试', {icon: 2});
                    }
                });
            },
            
            cancel: function(index) {
                console.log("取消审核");
            }
        });
    };
    
    console.log("✅ 审核弹窗函数已重新定义");
}

// 3. 重新定义删除确认函数
function fixConfirmDelete() {
    window.confirmDelete = function(id, projectName) {
        console.log("调用删除确认，ID:", id, "项目名称:", projectName);
        
        if (!id || !projectName) {
            console.error("删除参数不完整:", {id, projectName});
            layer.msg('参数错误', {icon: 2});
            return;
        }
        
        layer.confirm('确认要删除项目 "' + projectName + '" 吗？', {
            icon: 3,
            title: '删除确认'
        }, function(index) {
            layer.close(index);
            
            var loadingIndex = layer.load(1, {
                shade: [0.1, '#fff']
            });
            
            $.ajax({
                url: ctx + '/qqc/registration/delete',
                type: 'POST',
                data: {
                    id: id,
                    hdId: $("#competitionId").val() || $("#hdId").val()
                },
                success: function(result) {
                    layer.close(loadingIndex);
                    console.log("删除结果:", result);
                    
                    if(result.success) {
                        layer.msg('删除成功', {icon: 1});
                        location.reload();
                    } else {
                        layer.msg('删除失败: ' + result.msg, {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    layer.close(loadingIndex);
                    console.error("删除请求失败:", {xhr, status, error});
                    layer.msg('删除失败，请稍后重试', {icon: 2});
                }
            });
        });
    };
    
    console.log("✅ 删除确认函数已重新定义");
}

// 4. 重新定义退赛确认函数
function fixConfirmWithdraw() {
    window.confirmWithdraw = function(id, projectName) {
        console.log("调用退赛确认，ID:", id, "项目名称:", projectName);
        
        if (!id || !projectName) {
            console.error("退赛参数不完整:", {id, projectName});
            layer.msg('参数错误', {icon: 2});
            return;
        }
        
        layer.confirm('确认要为项目 "' + projectName + '" 申请退赛吗？', {
            icon: 3,
            title: '退赛确认'
        }, function(index) {
            layer.close(index);
            
            var loadingIndex = layer.load(1, {
                shade: [0.1, '#fff']
            });
            
            $.ajax({
                url: ctx + '/qqc/registration/withdraw',
                type: 'POST',
                data: {
                    id: id,
                    withdrawReason: '退赛'
                },
                success: function(result) {
                    layer.close(loadingIndex);
                    console.log("退赛结果:", result);
                    
                    if(result.success) {
                        layer.msg('退赛申请成功', {icon: 1});
                        location.reload();
                    } else {
                        layer.msg('退赛申请失败: ' + result.msg, {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    layer.close(loadingIndex);
                    console.error("退赛请求失败:", {xhr, status, error});
                    layer.msg('退赛申请失败，请稍后重试', {icon: 2});
                }
            });
        });
    };
    
    console.log("✅ 退赛确认函数已重新定义");
}

// 5. 检查并修复审核弹窗HTML
function fixAuditDialogHtml() {
    if ($('#auditDialog').length === 0) {
        console.log("创建审核弹窗HTML");
        
        const auditDialogHtml = `
        <div id="auditDialog" style="display: none; padding: 20px;">
            <form id="auditForm" class="form-horizontal audit-form">
                <input type="hidden" id="registrationId" name="id">
                <div class="form-group">
                    <label class="col-sm-3 control-label">项目名称：</label>
                    <div class="col-sm-9">
                        <p class="form-control-static" id="projectName"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">审核结果：</label>
                    <div class="col-sm-9">
                        <div class="radio">
                            <label>
                                <input type="radio" name="status" value="3"> 通过
                            </label>
                        </div>
                        <div class="radio">
                            <label>
                                <input type="radio" name="status" value="-1"> 退回
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group reject-reason" style="display: none;">
                    <label class="col-sm-3 control-label">退回理由：</label>
                    <div class="col-sm-9">
                        <textarea id="rejectReason" name="rejectReason" class="form-control" rows="5" placeholder="请填写退回理由"></textarea>
                    </div>
                </div>
            </form>
        </div>`;
        
        $('body').append(auditDialogHtml);
        console.log("✅ 审核弹窗HTML已创建");
    } else {
        console.log("✅ 审核弹窗HTML已存在");
    }
}

// 6. 确保ctx变量存在
function ensureCtxVariable() {
    if (typeof ctx === 'undefined') {
        // 尝试从页面中获取ctx值
        const ctxMeta = $('meta[name="ctx"]');
        if (ctxMeta.length > 0) {
            window.ctx = ctxMeta.attr('content');
        } else {
            // 默认值
            window.ctx = '';
        }
        console.log("✅ ctx变量已设置:", window.ctx);
    }
}

// 执行修复
function executeFixes() {
    if (!ensureLibrariesLoaded()) {
        return;
    }
    
    ensureCtxVariable();
    fixAuditDialogHtml();
    fixOpenAuditDialog();
    fixConfirmDelete();
    fixConfirmWithdraw();
    
    console.log("=== 修复完成 ===");
    console.log("现在可以尝试点击按钮测试功能");
}

// 运行修复
executeFixes();
