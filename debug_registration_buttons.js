// 报名详情按钮调试脚本
// 在浏览器控制台中运行此脚本来诊断问题

console.log("=== 报名详情按钮调试开始 ===");

// 1. 检查jQuery是否加载
if (typeof $ === 'undefined') {
    console.error("❌ jQuery未加载！");
} else {
    console.log("✅ jQuery已加载，版本:", $.fn.jquery);
}

// 2. 检查layer弹窗组件是否加载
if (typeof layer === 'undefined') {
    console.error("❌ Layer弹窗组件未加载！");
} else {
    console.log("✅ Layer弹窗组件已加载");
}

// 3. 检查审核弹窗HTML是否存在
const auditDialog = $('#auditDialog');
if (auditDialog.length === 0) {
    console.error("❌ 审核弹窗HTML元素 #auditDialog 不存在！");
} else {
    console.log("✅ 审核弹窗HTML元素存在");
    console.log("弹窗内容:", auditDialog.html().substring(0, 200) + "...");
}

// 4. 检查所有按钮是否存在
const buttons = {
    audit: $('a[onclick*="openAuditDialog"]'),
    delete: $('a[onclick*="confirmDelete"]'),
    withdraw: $('a[onclick*="confirmWithdraw"]')
};

console.log("=== 按钮检查 ===");
Object.keys(buttons).forEach(type => {
    const btn = buttons[type];
    console.log(`${type}按钮数量: ${btn.length}`);
    if (btn.length > 0) {
        btn.each(function(index) {
            const onclick = $(this).attr('onclick');
            console.log(`  ${type}按钮${index + 1}: ${onclick}`);
        });
    }
});

// 5. 检查函数是否定义
const functions = ['openAuditDialog', 'confirmDelete', 'confirmWithdraw'];
console.log("=== 函数检查 ===");
functions.forEach(funcName => {
    if (typeof window[funcName] === 'function') {
        console.log(`✅ 函数 ${funcName} 已定义`);
    } else {
        console.error(`❌ 函数 ${funcName} 未定义！`);
    }
});

// 6. 测试审核弹窗函数
console.log("=== 测试审核弹窗 ===");
if (typeof openAuditDialog === 'function') {
    try {
        // 创建测试函数，不实际执行
        console.log("尝试调用 openAuditDialog('test-id', 'test-project')...");
        // 注意：这里只是测试函数是否能被调用，不会实际打开弹窗
        const testResult = openAuditDialog.toString();
        console.log("✅ openAuditDialog 函数可以被调用");
    } catch (error) {
        console.error("❌ openAuditDialog 函数调用出错:", error);
    }
}

// 7. 检查特定数据行
console.log("=== 数据行检查 ===");
const tableRows = $('#registrationTable tbody tr');
console.log(`表格数据行数: ${tableRows.length}`);

tableRows.each(function(index) {
    const row = $(this);
    const auditBtn = row.find('a[onclick*="openAuditDialog"]');
    const deleteBtn = row.find('a[onclick*="confirmDelete"]');
    const withdrawBtn = row.find('a[onclick*="confirmWithdraw"]');
    
    console.log(`行 ${index + 1}:`);
    console.log(`  审核按钮: ${auditBtn.length > 0 ? '存在' : '不存在'}`);
    console.log(`  删除按钮: ${deleteBtn.length > 0 ? '存在' : '不存在'}`);
    console.log(`  退赛按钮: ${withdrawBtn.length > 0 ? '存在' : '不存在'}`);
    
    if (auditBtn.length > 0) {
        const onclick = auditBtn.attr('onclick');
        console.log(`  审核按钮onclick: ${onclick}`);
        
        // 提取ID和项目名称
        const match = onclick.match(/openAuditDialog\('([^']+)',\s*'([^']+)'\)/);
        if (match) {
            console.log(`  提取的ID: ${match[1]}`);
            console.log(`  提取的项目名: ${match[2]}`);
        }
    }
});

// 8. 检查控制台错误
console.log("=== 检查是否有JavaScript错误 ===");
console.log("请查看控制台是否有红色错误信息");

// 9. 提供手动测试函数
window.debugAuditDialog = function(id, projectName) {
    console.log("=== 手动测试审核弹窗 ===");
    console.log("ID:", id);
    console.log("项目名称:", projectName);
    
    try {
        openAuditDialog(id, projectName);
        console.log("✅ 审核弹窗调用成功");
    } catch (error) {
        console.error("❌ 审核弹窗调用失败:", error);
    }
};

// 10. 提供修复建议
console.log("=== 修复建议 ===");
console.log("1. 如果jQuery或Layer未加载，请检查页面引用");
console.log("2. 如果函数未定义，请检查JavaScript文件是否正确加载");
console.log("3. 如果审核弹窗HTML不存在，请检查JSP页面");
console.log("4. 可以使用 debugAuditDialog('test-id', 'test-project') 手动测试");

console.log("=== 报名详情按钮调试结束 ===");
